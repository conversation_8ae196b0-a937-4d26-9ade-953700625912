
🔬 โหมด: Experiment/Development
   - เทรนโมเดลใหม่
   - บันทึก ไฟล์โมเดล
   - ทดสอบ optimal parameters
🛡️ เริ่มใช้งาน Model Protection System (min profit: $5,000)
🚫 เริ่มใช้งาน Training Prevention System
🔧 โหลดการตั้งค่าป้องกัน Overfitting

🛡️ ระบบป้องกันโมเดล: FLEXIBLE
   🟡 เกณฑ์ยืดหยุ่น - สมดุลระหว่างคุณภาพและการยอมรับ
   - Accuracy ≥ 45.0%
   - Win Rate ≥ 35.0%
   - Expectancy ≥ 10.0

💡 แนวคิดการบันทึกโมเดล:
   🆕 โมเดลแรก: บันทึกเสมอ (ตามเกณฑ์ของแต่ละโหมด)
   🔄 โมเดลถัดไป: เปรียบเทียบกับโมเดลก่อนหน้า

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM/Multi
   📁 Hyperparameters: LightGBM/Hyper_Multi

🏗️ Creating Multi-Model Architecture Directories:
📁 Exists: LightGBM/Data_Trained (Data Storage)
📁 Exists: LightGBM/Hyper_Multi (Hyperparameters)
📁 Exists: LightGBM/Multi_Time (Time Used Folder)
📁 Exists: LightGBM/Multi (Main Multi-Model)
📁 Exists: LightGBM/Multi/feature_importance (Feature Importance)
📁 Exists: LightGBM/Multi/individual_performance (Performance Analysis)
📁 Exists: LightGBM/Multi/models (Models Base)
📁 Exists: LightGBM/Multi/results (Results)
📁 Exists: LightGBM/Multi/thresholds (Thresholds)
📁 Exists: LightGBM/Multi/training_summaries (Training Summaries)

🏗️ เปิดใช้งาน __name__
🏗️ เปิดใช้งาน run main analysis

================================================================================
🚀 เริ่มการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-10-01 16:39:50
🔢 จำนวนรอบทั้งหมด: 1
📊 จำนวนกลุ่มข้อมูล: 1
2025-10-01 16:39:50 | INFO     | FinancialAnalysis | 🚀 Enhanced Logging System เริ่มทำงาน
2025-10-01 16:39:50 | INFO     | FinancialAnalysis | 🏗️ เปิดใช้งาน Financial Analysis System | base_currency=USD | leverage=500
✅ เปิดใช้งานระบบวิเคราะห์ทางการเงิน (Account Balance: $1,000.00)
📁 จำนวนไฟล์ทั้งหมด: 1
   - M60: 1 ไฟล์
================================================================================

============================================================
🔄 รอบที่ 1/1
============================================================
⏰ เวลาเริ่มรอบ: 16:39:50
📊 ประมวลผลกลุ่ม M60 (1 ไฟล์)

🏗️ เปิดใช้งาน main

🔍 ตรวจสอบการป้องกันการเทรนสำหรับ GBPUSD M60
✅ สามารถเทรนได้: allowed

🔍 กำลังค้นหาพารามิเตอร์ที่เหมาะสมสำหรับ GBPUSD M60...
📁 ใช้ไฟล์: multi_asset_results_20250928_095554.json (Modified: 2025-09-28 09:55)
======================================================================
🎯 PARAMETER OPTIMIZATION RESULTS
======================================================================
📊 Source: GBPUSD_M60 specific

🔸 GBPUSD_M60
   Score: 57.87
   Win Rate: 50.0%
   Total Profit: $73
   Total Trades: 28
   Expectancy: 2.60
   Max Drawdown: $62
   
   Best Parameters:
     SL ATR: 1.0
     TP Ratio: 2.0
     RSI Level: 25
     Volume Spike: 1.0
======================================================================

🔄 Updated Global Parameters:
   input_stop_loss_atr: 1.0 (unchanged)
   input_take_profit: 2.0 (unchanged)
   input_rsi_level_in: 35 → 25
   input_volume_spike: 1.25 → 1.0
   input_rsi_level_over: 70 (unchanged)
   input_rsi_level_out: 30 → 35
   input_pull_back: 0.5 → 0.45
   input_initial_nbar_sl: 4 (unchanged)

============================================================
🚀 เริ่มต้นการวิเคราะห์ Multi-Model Architecture
============================================================
📋 ขั้นตอนการทำงาน:
   1. โหลดและประมวลผลข้อมูล
   2. เทรนโมเดล Multi-Model Architecture
   3. ทดสอบ Optimal Threshold
   4. ทดสอบ Optimal nBars SL
   5. บันทึกผลลัพธ์และพารามิเตอร์
============================================================
file CSV_Files_Fixed/GBPUSD_H1_FIXED.csv main_round 1 symbol GBPUSD timeframe M60

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
⚠️ ไม่พบไฟล์ threshold สำหรับ GBPUSD MM60, ใช้ค่า default: 0.25

🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ trend_following, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M60_GBPUSD_trend_following_optimal_nBars_SL.pkl')
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ counter_trend, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M60_GBPUSD_counter_trend_optimal_nBars_SL.pkl')
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ GBPUSD MM60, ใช้ค่า default: 4

📂 โหลดข้อมูลจาก LightGBM/Data_Trained/M60_GBPUSD_features.csv

🔍 ตรวจสอบ columns ข้อมูล df ขั้นตอน create_features() : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

✅ ข้อมูลที่ส่งเข้า load_and_process_data : nBars_SL 4 confidence 0.25

🏗️ เปิดใช้งาน load and process data

🔍 กำลังตรวจสอบและจัดการ Missing Values...
✅ ไม่พบ Missing Values ในข้อมูล

🔍 กำลังสร้าง trade cycles...

🔄 Multi-Model: กำลังโหลด features จาก LightGBM/Multi/models/trend_following\M60_GBPUSD_features.pkl
⚠️ ไม่พบไฟล์รายชื่อ Features ที่ Model ใช้: LightGBM/Multi/models/trend_following\M60_GBPUSD_features.pkl
🔎 ใช้ entry_func (Multi-Model default): trend_following

🔄 โหลดโมเดล Multi-Model Architecture สำหรับ GBPUSD MM60

🏗️ เปิดใช้งาน load scenario models

🔍 ตรวจสอบโมเดลที่มีอยู่สำหรับ GBPUSD MM60
❌ trend_following: ไม่พบไฟล์โมเดล
❌ trend_following_Buy: ไม่พบไฟล์โมเดล
❌ trend_following_Sell: ไม่พบไฟล์โมเดล
❌ counter_trend: ไม่พบไฟล์โมเดล
❌ counter_trend_Buy: ไม่พบไฟล์โมเดล
❌ counter_trend_Sell: ไม่พบไฟล์โมเดล

📊 สรุป: 0/6 โมเดลพร้อมใช้งาน
⚠️ โมเดลที่ขาดหายไป: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
❌ ไม่พบโมเดลใดเลยสำหรับ GBPUSD MM60
❌ ไม่สามารถโหลดโมเดล Multi-Model ได้ - ใช้ Technical Analysis แทน

🏗️ เปิดใช้งาน try trade with threshold adjustment

📂 พบ threshold ที่บันทึกไว้สำหรับ GBPUSD_M60_technical
   ค่า threshold: 0.2
   จำนวน trades: 9110
   ⏱️ ข้อมูลบันทึกเมื่อ 3 วัน 3 ชั่วโมงที่แล้ว (ยังใช้ได้)

🔍 ทดสอบ threshold ที่บันทึกไว้: 0.2000

🏗️ เปิดใช้งาน create trade cycles with model : reduce factor 0.8000

📊 ใช้ Single-Model Architecture
ตรวจสอบการใช้ Model ML : False
model_features: None

❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม
🔍 ใช้ start_index = 50 (ข้อมูลทั้งหมด 74351 แถว)

▶️ เริ่ม Backtest จาก index: {start_index}

🔍 สรุปการตรวจสอบ Look-Ahead Bias
- จำนวนการทำนายทั้งหมด: 74301
- จำนวนครั้งที่พบ NaN ใน Features: 0
- จำนวนครั้งที่พบ Features อาจมีปัญหา: 0
✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)
💰 เริ่มการวิเคราะห์ทางการเงินสำหรับ GBPUSD M60
🔄 กำลังประมวลผล GBPUSD M60...
2025-10-01 16:42:29 | INFO     | FinancialAnalysis | 🏗️ เปิดใช้งาน process_trade_cycle | symbol=GBPUSD | timeframe=M60 | trades_count=16144
2025-10-01 16:42:31 | INFO     | FinancialAnalysis | 💰 Financial Analysis: GBPUSD_M60 | symbol=GBPUSD | timeframe=M60 | total_trades=16144 | total_profit_usd=-332746.********** | max_drawdown_usd=334395.*********** | margin_per_trade=252.99999999999997
💾 บันทึกการวิเคราะห์ GBPUSD M60 ที่: Financial_Analysis_Results/GBPUSD_M60_financial_analysis.json
✅ ประมวลผล GBPUSD M60 สำเร็จ: 16144 รายการ
✅ วิเคราะห์ทางการเงิน GBPUSD M60 สำเร็จ
✅ ยืนยัน threshold ที่บันทึกไว้ใช้ได้ดี: พบ 16144 trades
🎉 สำเร็จ! ใช้ threshold สุดท้าย: 0.2000 (Technical Analysis)
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_06a_trade_df.csv

📌 ข้อมูลก่อนตรวจสอบ:
จำนวนแถวข้อมูลทั้งหมด: 74351 ตัวอย่างข้อมูล df
         Date      Time     Open     High      Low  ...  D1_Volume_Momentum  D1_Volume_TrendStrength D1_MACD_line  D1_MACD_deep  D1_MACD_signal
0  2013.07.18  13:00:00  1.52186  1.52304  1.52052  ...                 1.0                      0.0          0.0           0.0             0.0
1  2013.07.18  14:00:00  1.52178  1.52209  1.51857  ...                 1.0                      0.0          0.0           0.0             0.0
2  2013.07.18  15:00:00  1.51909  1.52168  1.51863  ...                 1.0                      0.0          0.0           0.0             0.0
3  2013.07.18  16:00:00  1.52072  1.52126  1.51820  ...                 1.0                      0.0          0.0           0.0             0.0
4  2013.07.18  17:00:00  1.51995  1.52075  1.51907  ...                 1.0                      0.0          0.0           0.0             0.0

[5 rows x 332 columns]
จำนวนการซื้อขายที่พบ: 16144 ตัวอย่างข้อมูล trade_df
           Entry Time  Entry Price           Exit Time  Exit Price  ...  Pct_Risk  Pct_Reward  Volume MA20 at Entry  Volume Spike at Entry
0 2013-07-23 04:00:00      1.53645 2013-07-23 07:00:00     1.53645  ...  0.000000    0.001159               1588.00                      0
1 2013-07-23 10:00:00      1.53618 2013-07-23 10:00:00     1.53618  ...  0.000000    0.001107               1425.45                      1
2 2013-07-23 13:00:00      1.53500 2013-07-23 13:00:00     1.53366  ...  0.000873    0.001752               1341.85                      1
3 2013-07-23 15:00:00      1.53514 2013-07-23 18:00:00     1.53514  ...  0.000000    0.002156               1393.00                      1
4 2013-07-23 19:00:00      1.53770 2013-07-23 21:00:00     1.53770  ...  0.000000    0.002653               1588.30                      1

[5 rows x 20 columns]

📊 สถิติการซื้อขาย:
========================================
ประเภท              ค่าสถิติ            
----------------------------------------
🏗️ เปิดใช้งาน analyze trade performance
📈 สถิติสำหรับ Buy Trades:
Win%                24.23               
Expectancy          -41.40              
📈 สถิติสำหรับ Sell Trades:
Win%                26.01               
Expectancy          -35.01              
📈 สถิติสำหรับ Buy_sell Trades:
Win%                25.09               
Expectancy          -38.30              
========================================

📊 สถิติรายวัน:
วัน       Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
Monday    13.31          3013                
Tuesday   14.35          3331                
Wednesday 12.67          3292                
Thursday  13.88          3344                
Friday    13.27          3164                
========================================

📊 สถิติรายชั่วโมง:
ชั่วโมง   Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
4         12.84          1075                
5         12.36          712                 
6         16.38          525                 
7         13.07          436                 
8         14.08          426                 
9         16.64          559                 
10        13.03          975                 
11        13.80          1138                
12        12.46          1228                
13        14.31          1118                
14        13.31          1044                
15        14.80          1047                
16        13.72          1166                
17        16.24          1164                
18        9.91           1160                
19        13.72          940                 
20        12.91          728                 
21        11.38          703                 
========================================

🔍 กำลังรวม features กับ trade data...

ตัวอย่าง Entry_DateTime ใน trade_df: 0   2013-07-23 04:00:00
1   2013-07-23 10:00:00
2   2013-07-23 13:00:00
3   2013-07-23 15:00:00
4   2013-07-23 19:00:00
Name: Entry_DateTime, dtype: datetime64[ns]
ตัวอย่าง Merge_Key_Time ที่จะใช้ join: 0   2013-07-23 03:55:00
1   2013-07-23 09:55:00
2   2013-07-23 12:55:00
3   2013-07-23 14:55:00
4   2013-07-23 18:55:00
Name: Merge_Key_Time, dtype: datetime64[ns]
ตัวอย่าง DateTime ใน df: 0   2013-07-18 13:00:00
1   2013-07-18 14:00:00
2   2013-07-18 15:00:00
3   2013-07-18 16:00:00
4   2013-07-18 17:00:00
Name: DateTime, dtype: datetime64[ns]
🔍 กำลังทำการ merge_asof โดยใช้คอลัมน์ 313 features : ['DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']
🔧 คำนวณฟีเจอร์เวลา (Hour, DayOfWeek) จาก Timestamp ของ Feature ที่ถูกต้อง (แท่งก่อนหน้า)

📌 ตรวจสอบ Missing Values หลัง Merge:
✅ ไม่พบ Missing Values ใน DataFrame

📌 ข้อมูลหลังรวม features:
จำนวนแถว: 16144
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  Entry Hour
0 2013-07-23 04:00:00      1.53645 2013-07-23 07:00:00     1.53645        Buy     0.0          1           4
1 2013-07-23 10:00:00      1.53618 2013-07-23 10:00:00     1.53618        Buy     0.0          1          10
2 2013-07-23 13:00:00      1.53500 2013-07-23 13:00:00     1.53366        Buy  -134.0          1          13
3 2013-07-23 15:00:00      1.53514 2013-07-23 18:00:00     1.53514        Buy     0.0          1          15
4 2013-07-23 19:00:00      1.53770 2013-07-23 21:00:00     1.53770        Buy     0.0          1          19

🔍 กำลังสร้าง target variable...

🔍 กำลังสร้าง target variable...

✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_06b_trade_df_merge.csv
🔍 ตรวจสอบ columns ข้อมูล df หลัง merge : จำนวน 332
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

🏗️ เปิดใช้งาน process trade targets
📊 Profit column status: 16144/16144 valid values

🏗️ เปิดใช้งาน create multiclass target (Logic ใหม่)
📊 Multi-class Target Statistics:
  Class 0 (strong_sell): 1087 samples (6.7%)
  Class 1 (weak_sell): 3557 samples (22.0%)
  Class 2 (no_trade): 6496 samples (40.2%)
  Class 3 (weak_buy): 3925 samples (24.3%)
  Class 4 (strong_buy): 1079 samples (6.7%)
✅ Multi-class Target ถูกต้อง: 5 classes พร้อมใช้งาน

📊 Multi-class Target Distribution:
Target_Multiclass
0    1087
1    3557
2    6496
3    3925
4    1079
Name: count, dtype: int64
Class 0 (strong_sell): 1087 trades, Profit range: 91.0 to 5597.0
Class 1 (weak_sell): 3557 trades, Profit range: -20.0 to 54.0
Class 2 (no_trade): 6496 trades, Profit range: -1500.0 to -21.0
Class 3 (weak_buy): 3925 trades, Profit range: -16.0 to 60.0
Class 4 (strong_buy): 1079 trades, Profit range: 72.0 to 1272.0

✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_07_trade_df_valid_trades.csv
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📊 การกระจายของ Target ต่างๆ:
1. Target หลัก (Binary):
Main_Target
0    13979
1     2165
Name: count, dtype: int64

2. Target สำหรับ Buy Trades:
Target_Buy
0    7315
1    1078
Name: count, dtype: int64

3. Target สำหรับ Sell Trades:
Target_Sell
0    6664
1    1087
Name: count, dtype: int64

✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_08a_trade_df_target.csv
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📌 ข้อมูลหลังสร้าง target variable:
จำนวนแถว: 16144
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time     EMA50    EMA100    EMA200      RSI14
0 2013-07-23 04:00:00  1.529877  1.524634  1.516108  69.391558
1 2013-07-23 10:00:00  1.531275  1.525970  1.517293  62.313563
2 2013-07-23 13:00:00  1.531660  1.526477  1.517807  52.796296
3 2013-07-23 15:00:00  1.531851  1.526777  1.518130  53.203727
4 2013-07-23 19:00:00  1.532457  1.527481  1.518827  60.956506

📌 ข้อมูลหลังสร้าง target:
การกระจายของ Target:
Target
0    13979
1     2165
Name: count, dtype: int64

🔍 เริ่มกระบวนการเลือก Features...

🏗️ เปิดใช้งาน select features
⚠️ Feature 'Volume_MA_20' not found in DataFrame. Skipping.
ℹ️ Potential features for model input (Pre-Trade Only): ['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_15', 'ADX_zone_25', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']+['Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20'] = 416 features considered.

🔍 ความสัมพันธ์กับ Target (ทั้งหมด):
Target                      1.000000
Rolling_Close_15            0.019516
D1_Bar_TL                   0.019276
RSI14_x_Volume              0.018837
Volume_Change_3             0.018274
                              ...   
Momentum5_x_Volatility10    0.000095
D1_Bar_CL_HL                0.000012
RSI_Divergence_i6                NaN
RSI_Divergence_i4                NaN
RSI_Divergence_i2                NaN
Name: Target, Length: 301, dtype: float64

📊 ค่า VIF ของ Features:
                  feature         VIF
0        Rolling_Close_15   11.950801
1               D1_Bar_TL    1.022620
2          RSI14_x_Volume    2.537560
3         Volume_Change_3    1.260475
4              H8_Bar_DTB    1.031876
5            Slope_EMA200         inf
6                  Bar_CL         inf
7         Volume_Change_2    1.020678
8            RSI_Oversold    2.869420
9              ATR_ROC_i2    5.470704
10    EMA50_x_RollingVol5   53.193322
11              RSI_trend         inf
12            RSI_counter         inf
13        D1_Bar_longwick    1.075453
14            Price_Range    3.209773
15          Volume_Lag_50    1.848071
16        Rolling_Close_5   56.722246
17        RSI_signal_EMA4    3.949029
18  ADX_14_x_RollingVol15    5.057606
19               ATR_Deep    2.266445
20              ATR_Lag_3   43.293781
21       RSI_signal_EMA12    5.373353
22              ATR_Lag_2  147.682381
23              ATR_Lag_1   94.886105
24           Slope_EMA100    4.620995
25                IsNight    1.209873
26             H12_Bar_SW    1.741755
27            H12_Bar_DTB    1.031252
28            Dist_EMA200  845.344069
29       EMA_diff_100_200         inf
30              Bar_CL_OC    6.395923
31       EMA_diff_x_RSI14   18.259891
32        RSI_signal_EMA8    6.713680
33          H8_Price_Move    1.876236
34           H2_MACD_line    3.386422
35        EMA_diff_50_200         inf
36              H4_Bar_SW    1.610251
37      PullBack_100_Down   15.320726
38        PullBack_100_Up   14.592998
39        H4_Bar_longwick    1.070760
40             H4_Bar_DTB    1.006365
41             H4_Bar_OSB    1.149315
42           H4_MACD_deep    2.283531
43         H8_MACD_signal    2.659729
44        Volume_Momentum    1.353719
45            ADX_zone_15    1.130292
46        MA_Cross_50_100    4.017234
47                 Bar_SW    2.649825
48        MA_Cross_50_200    5.396423
49         H4_Price_Range    2.907665
50           H8_MACD_line    4.178011
51        EMA_diff_50_100         inf
52            Dist_EMA100  483.287971
53             RSI_ROC_i4    3.050532
54                 Bar_TL    1.065656
55         H8_Price_Range    3.276383
56            H12_Bar_OSB    1.112373

🚫 Features ถูกตัดออกเนื่องจาก VIF สูง: ['Rolling_Close_15', 'Slope_EMA200', 'Bar_CL', 'EMA50_x_RollingVol5', 'RSI_trend', 'RSI_counter', 'Rolling_Close_5', 'ATR_Lag_3', 'ATR_Lag_2', 'ATR_Lag_1', 'Dist_EMA200', 'EMA_diff_100_200', 'EMA_diff_x_RSI14', 'EMA_diff_50_200', 'PullBack_100_Down', 'PullBack_100_Up', 'EMA_diff_50_100', 'Dist_EMA100']

🔍 เริ่มการคัดเลือก features ด้วย RFE และ Feature Importance
[LightGBM] [Info] Number of positive: 2165, number of negative: 13979
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.005017 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3388
[LightGBM] [Info] Number of data points in the train set: 16144, number of used features: 39
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134106 -> initscore=-1.865136
[LightGBM] [Info] Start training from score -1.865136
[LightGBM] [Info] Number of positive: 2165, number of negative: 13979
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.005129 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3385
[LightGBM] [Info] Number of data points in the train set: 16144, number of used features: 38
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134106 -> initscore=-1.865136
[LightGBM] [Info] Start training from score -1.865136
[LightGBM] [Info] Number of positive: 2165, number of negative: 13979
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004297 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3382
[LightGBM] [Info] Number of data points in the train set: 16144, number of used features: 37
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134106 -> initscore=-1.865136
[LightGBM] [Info] Start training from score -1.865136
[LightGBM] [Info] Number of positive: 2165, number of negative: 13979
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004057 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3379
[LightGBM] [Info] Number of data points in the train set: 16144, number of used features: 36
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134106 -> initscore=-1.865136
[LightGBM] [Info] Start training from score -1.865136
[LightGBM] [Info] Number of positive: 2165, number of negative: 13979
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004730 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3377
[LightGBM] [Info] Number of data points in the train set: 16144, number of used features: 35
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134106 -> initscore=-1.865136
[LightGBM] [Info] Start training from score -1.865136
[LightGBM] [Info] Number of positive: 2165, number of negative: 13979
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004127 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3374
[LightGBM] [Info] Number of data points in the train set: 16144, number of used features: 34
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134106 -> initscore=-1.865136
[LightGBM] [Info] Start training from score -1.865136
[LightGBM] [Info] Number of positive: 2165, number of negative: 13979
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.005995 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3371
[LightGBM] [Info] Number of data points in the train set: 16144, number of used features: 33
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134106 -> initscore=-1.865136
[LightGBM] [Info] Start training from score -1.865136
[LightGBM] [Info] Number of positive: 2165, number of negative: 13979
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003577 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3369
[LightGBM] [Info] Number of data points in the train set: 16144, number of used features: 32
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134106 -> initscore=-1.865136
[LightGBM] [Info] Start training from score -1.865136
[LightGBM] [Info] Number of positive: 2165, number of negative: 13979
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003495 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3366
[LightGBM] [Info] Number of data points in the train set: 16144, number of used features: 31
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134106 -> initscore=-1.865136
[LightGBM] [Info] Start training from score -1.865136
[LightGBM] [Info] Number of positive: 2165, number of negative: 13979
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003902 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3363
[LightGBM] [Info] Number of data points in the train set: 16144, number of used features: 30
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134106 -> initscore=-1.865136
[LightGBM] [Info] Start training from score -1.865136
[LightGBM] [Info] Number of positive: 2165, number of negative: 13979
[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.001560 seconds.
You can set `force_row_wise=true` to remove the overhead.
And if memory is not enough, you can set `force_col_wise=true`.
[LightGBM] [Info] Total Bins 3360
[LightGBM] [Info] Number of data points in the train set: 16144, number of used features: 29
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134106 -> initscore=-1.865136
[LightGBM] [Info] Start training from score -1.865136
[LightGBM] [Info] Number of positive: 2165, number of negative: 13979
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004376 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3358
[LightGBM] [Info] Number of data points in the train set: 16144, number of used features: 28
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134106 -> initscore=-1.865136
[LightGBM] [Info] Start training from score -1.865136
[LightGBM] [Info] Number of positive: 2165, number of negative: 13979
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003626 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3355
[LightGBM] [Info] Number of data points in the train set: 16144, number of used features: 27
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134106 -> initscore=-1.865136
[LightGBM] [Info] Start training from score -1.865136

✅ RFE เลือก 27 features จากทั้งหมด 39 features
📋 Top 10 features จาก RFE:
1. D1_Bar_TL
2. RSI14_x_Volume
3. Volume_Change_3
4. Volume_Change_2
5. ATR_ROC_i2
6. D1_Bar_longwick
7. Price_Range
8. Volume_Lag_50
9. ADX_14_x_RollingVol15
10. RSI_signal_EMA12
[LightGBM] [Info] Number of positive: 2165, number of negative: 13979
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004693 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 3388
[LightGBM] [Info] Number of data points in the train set: 16144, number of used features: 39
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.134106 -> initscore=-1.865136
[LightGBM] [Info] Start training from score -1.865136

✅ Feature Importance เลือก 13 features จากทั้งหมด 39 features
📋 Top 10 features จาก Feature Importance:
1. RSI14_x_Volume
2. Volume_Change_3
3. Volume_Change_2
4. ATR_ROC_i2
5. D1_Bar_longwick
6. Price_Range
7. Volume_Lag_50
8. ADX_14_x_RollingVol15
9. H8_Price_Move
10. H4_Bar_longwick

✅ รวมทั้งสองวิธีได้ 27 features ที่สำคัญ

เริ่มขั้นตอนการตรวจสอบ features ที่ต้องใช้จากการทำ analyze cross asset feature importance

featuresasset_feature_importance : len  6
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']
👍 เพิ่ม Feature ที่จำเป็น 'DayOfWeek' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Hour' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsMorning' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsAfternoon' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsEvening' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsNight' เข้าไปในรายการ

🔍 จำนวน features ที่เลือกได้: 33

✅ Final selected features for training: 33 features
📋 Top 10 features:
1. D1_Bar_TL (corr: 0.0193)
2. H4_Bar_SW (corr: 0.0124)
3. H8_Price_Range (corr: 0.0103)
4. ADX_14_x_RollingVol15 (corr: 0.0151)
5. H12_Bar_SW (corr: 0.0131)
6. MA_Cross_50_100 (corr: 0.0118)
7. H4_Price_Range (corr: 0.0106)
8. H8_MACD_line (corr: 0.0106)
9. H4_MACD_deep (corr: 0.0121)
10. Price_Range (corr: 0.0154)
... และอีก 23 features
💾 บันทึก features (pkl): LightGBM/Multi\feature_selected\GBPUSD_M60_selected_features.pkl
📝 บันทึก features (txt): LightGBM/Multi\feature_selected\GBPUSD_M60_selected_features.txt

📌 สรุป Features ที่จะใช้สำหรับโมเดล:
จำนวน Features: 33
1. D1_Bar_TL
2. H4_Bar_SW
3. H8_Price_Range
4. ADX_14_x_RollingVol15
5. H12_Bar_SW
6. MA_Cross_50_100
7. H4_Price_Range
8. H8_MACD_line
9. H4_MACD_deep
10. Price_Range
11. RSI_signal_EMA12
12. H8_Price_Move
13. Volume_Momentum
14. Bar_TL
15. H12_Bar_OSB
... และอีก 18 features

🔍 กำลังตรวจสอบข้อมูลก่อนฝึกโมเดล...

📊 การกระจายของ Target:
Target
0    0.865894
1    0.134106
Name: proportion, dtype: float64
อัตราส่วน Class Imbalance: 0.15
⚠️ พบ Class Imbalance รุนแรง (อัตราส่วนน้อยกว่า 20%)
✅ ไม่พบ missing values ใน features

📊 สถิติพื้นฐานของ features:
                         count           mean            std         min           25%            50%            75%           max
D1_Bar_TL              16144.0       0.166068       0.372153    0.000000      0.000000       0.000000       0.000000  1.000000e+00
H4_Bar_SW              16144.0       0.022919       0.726552   -1.000000     -1.000000       0.000000       1.000000  1.000000e+00
H8_Price_Range         16144.0       0.005365       0.004216    0.000550      0.002830       0.004340       0.006700  1.791200e-01
ADX_14_x_RollingVol15  16144.0       0.026194       0.025447    0.001861      0.013191       0.020490       0.031778  8.082250e-01
H12_Bar_SW             16144.0       0.003964       0.731828   -1.000000     -1.000000       0.000000       1.000000  1.000000e+00
MA_Cross_50_100        16144.0       0.025768       0.999699   -1.000000     -1.000000       1.000000       1.000000  1.000000e+00
H4_Price_Range         16144.0       0.004223       0.003165    0.000060      0.002240       0.003550       0.005360  1.354100e-01
H8_MACD_line           16144.0       0.022671       0.999774   -1.000000     -1.000000       1.000000       1.000000  1.000000e+00
H4_MACD_deep           16144.0       0.020565       0.999819   -1.000000     -1.000000       1.000000       1.000000  1.000000e+00
Price_Range            16144.0       0.002700       0.002143    0.000060      0.001450       0.002220       0.003330  9.566000e-02
RSI_signal_EMA12       16144.0       0.021556       0.999799   -1.000000     -1.000000       1.000000       1.000000  1.000000e+00
H8_Price_Move          16144.0       0.000152       0.004080   -0.147820     -0.001480       0.000260       0.001900  3.470000e-02
Volume_Momentum        16144.0       0.063739       0.989301   -1.000000     -1.000000       1.000000       1.000000  1.000000e+00
Bar_TL                 16144.0       0.206392       0.404728    0.000000      0.000000       0.000000       0.000000  1.000000e+00
H12_Bar_OSB            16144.0      -0.003655       0.338599   -1.000000      0.000000       0.000000       0.000000  1.000000e+00
Volume_Change_2        16144.0       0.428287       5.754627   -1.000000     -0.178277       0.099459       0.619956  7.190000e+02
Bar_SW                 16144.0       0.017839       0.687535   -1.000000      0.000000       0.000000       0.000000  1.000000e+00
D1_Bar_longwick        16144.0      -0.178671       1.386470  -15.915493     -0.741497      -0.206564       0.521779  6.710280e+00
H4_Bar_longwick        16144.0      -0.044114       2.127473 -111.500000     -0.651416      -0.039870       0.620536  3.860000e+01
RSI14_x_Volume         16144.0  171707.945105  137376.888666    0.000000  82611.619202  135800.281737  221409.104528  4.305813e+06
RSI_ROC_i4             16144.0      -0.035110       0.261829   -2.877162     -0.135228       0.007417       0.124826  6.732853e-01
Bar_CL_OC              16144.0       0.032891       0.825458   -1.000000     -1.000000       0.000000       1.000000  1.000000e+00
Volume_Lag_50          16144.0    2982.095577    2281.437799    0.000000   1391.000000    2470.000000    3885.000000  5.564200e+04
Volume_Change_3        16144.0       0.760177       2.391475   -1.000000     -0.156327       0.222561       1.000000  8.275926e+01
MA_Cross_50_200        16144.0       0.025149       0.999715   -1.000000     -1.000000       1.000000       1.000000  1.000000e+00
H8_MACD_signal         16144.0       0.007867       0.999969   -1.000000     -1.000000       1.000000       1.000000  1.000000e+00
ATR_ROC_i2             16144.0       0.051846       0.136420   -0.781339     -0.023743       0.073682       0.136512  6.132295e-01
DayOfWeek              16144.0       2.019140       1.394343    0.000000      1.000000       2.000000       3.000000  4.000000e+00
Hour                   16144.0      12.063491       4.895285    0.000000      9.000000      12.000000      16.000000  2.300000e+01
IsMorning              16144.0       0.241080       0.427752    0.000000      0.000000       0.000000       0.000000  1.000000e+00
IsAfternoon            16144.0       0.270999       0.444489    0.000000      0.000000       0.000000       1.000000  1.000000e+00
IsEvening              16144.0       0.247522       0.431586    0.000000      0.000000       0.000000       0.000000  1.000000e+00
IsNight                16144.0       0.110567       0.313605    0.000000      0.000000       0.000000       0.000000  1.000000e+00

🔍 กำลังตรวจสอบความสัมพันธ์ระหว่าง features...
⚠️ พบ 1 คู่ features ที่มีความสัมพันธ์สูง (>0.8)

คู่ features ที่มีความสัมพันธ์สูง:
      Feature 1       Feature 2  Correlation
   H8_MACD_line MA_Cross_50_200     0.836133
MA_Cross_50_200    H8_MACD_line     0.836133

🔍 กำลังแบ่งข้อมูลเป็น train/val/test...

📊 ใช้ Target Column: Target_Multiclass
📊 การกระจายของ Target ทั้งหมด:
Target_Multiclass
2    6496
3    3925
1    3557
0    1087
4    1079
Name: count, dtype: int64

🔄 ใช้ Stratified Time Series Split เพื่อรักษา positive samples ใน validation set
📊 Total positive samples: 3557
📊 Total negative samples: 1087
📊 Positive distribution - Train: 2135, Val: 711, Test: 711

📊 การกระจายของ Target ในชุดข้อมูลหลังแบ่ง:
Train: 2788 samples, positive: 2135 (76.6%)
Val: 928 samples, positive: 711 (76.6%)
Test: 928 samples, positive: 711 (76.6%)
Train distribution: Target_Multiclass
1    0.765782
0    0.234218
Name: proportion, dtype: float64
Val distribution: Target_Multiclass
1    0.766164
0    0.233836
Name: proportion, dtype: float64
Test distribution: Target_Multiclass
1    0.766164
0    0.233836
Name: proportion, dtype: float64

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 1 : จำนวน 338

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 2 : จำนวน 338
✅ บันทึกไฟล์ train, val, test เรียบร้อย

🏗️ เปิดใช้งาน analyze time filters (Enhanced)
📊 Adaptive Thresholds - Win Rate: 0.300, Expectancy: 74.734
⚠️ ไม่มีวันใดผ่านเกณฑ์เข้มงวด - เลือกวันที่ดีที่สุด 3 วัน
⚠️ ไม่มีชั่วโมงใดผ่านเกณฑ์เข้มงวด - เลือกชั่วโมงที่ดีที่สุด 8 ชั่วโมง

📊 Enhanced Time Filter Analysis for GBPUSD:
📅 Recommended Days: ['Thursday', 'Tuesday', 'Friday']
⏰ Recommended Hours: [5, 8, 12, 13, 16, 18, 19, 20]
🕐 Continuous Time Blocks: ['12:00-13:59', '18:00-20:59']
📈 Performance Improvement: {'win_rate_improvement': 0.0, 'expectancy_improvement': 41.420705424725796, 'filtered_trades': 246, 'total_trades': 928, 'coverage': 0.2650862068965517, 'filtered_win_rate': 0.0, 'all_win_rate': 0.0}
✅ บันทึก time filter ที่: LightGBM/Multi/thresholds/M60_GBPUSD_time_filters.pkl

🔍 กำลังทำ Feature Scaling...
🔍 ตรวจสอบข้อมูลก่อน Feature Scaling...
X_train shape: (2788, 33)
🔍 ตรวจสอบ infinity และ extreme values...
🔍 ตรวจสอบสุดท้าย: infinity=False, NaN=False, extreme=True
⚠️ ยังพบปัญหา - ใช้การแก้ไขแบบเข้มงวดสุด
✅ ใช้การแก้ไขแบบเข้มงวดสุด (แทนที่ด้วย 0 และ clip ค่า)
📊 ข้อมูลสุดท้าย: X_train shape=(2788, 33), min=-47.67, max=1000000.00
✅ ทำ Feature Scaling เรียบร้อยแล้ว
train_data : (      D1_Bar_TL  H4_Bar_SW  H8_Price_Range  ADX_14_x_RollingVol15  H12_Bar_SW  ...      Hour  IsMorning  IsAfternoon  IsEvening   IsNight
30    -0.415227   0.050866       -0.509745               0.019377   -1.076980  ...  0.584304  -0.589501     1.607342  -0.566305 -0.341379
31    -0.415227  -1.339481        0.402878               0.225766   -1.076980  ...  1.213197  -0.589501    -0.622145   1.765834 -0.341379
33    -0.415227  -1.339481       -0.566102               0.025576   -1.076980  ... -0.463849   1.696351    -0.622145  -0.566305 -0.341379
34    -0.415227  -1.339481       -0.566102               1.043666   -1.076980  ...  0.374674  -0.589501     1.607342  -0.566305 -0.341379
37    -0.415227   1.441214        1.180972               1.060490    0.347413  ... -1.931265  -0.589501    -0.622145  -0.566305  2.929293
...         ...        ...             ...                    ...         ...  ...       ...        ...          ...        ...       ...
9606   2.408319   1.441214       -0.446116              -0.372989    1.771806  ...  0.165043  -0.589501     1.607342  -0.566305 -0.341379
9608   2.408319  -1.339481        1.028262               0.246790    1.771806  ...  0.793935  -0.589501    -0.622145   1.765834 -0.341379
9609   2.408319  -1.339481        1.028262               0.864244    1.771806  ...  1.003566  -0.589501    -0.622145   1.765834 -0.341379
9621  -0.415227   1.441214        0.295618              -0.289511    0.347413  ...  1.213197  -0.589501    -0.622145   1.765834 -0.341379
9624  -0.415227   1.441214       -0.098883              -0.540079    0.347413  ...  0.374674  -0.589501     1.607342  -0.566305 -0.341379

[2788 rows x 33 columns], 30      0
31      1
33      1
34      1
37      1
       ..
9606    0
9608    0
9609    0
9621    0
9624    0
Name: Target_Multiclass, Length: 2788, dtype: int32)
val_data : (       D1_Bar_TL  H4_Bar_SW  H8_Price_Range  ADX_14_x_RollingVol15  H12_Bar_SW  ...      Hour  IsMorning  IsAfternoon  IsEvening   IsNight
9163   -0.415227   1.441214        0.366519               0.520242    0.347413  ...  1.003566  -0.589501    -0.622145   1.765834 -0.341379
9164   -0.415227  -1.339481       -0.338855              -0.075217   -1.076980  ...  0.165043  -0.589501     1.607342  -0.566305 -0.341379
9166   -0.415227   1.441214        0.002924              -0.297240    0.347413  ... -1.931265  -0.589501    -0.622145  -0.566305  2.929293
9167   -0.415227  -1.339481       -0.366125              -0.427826    0.347413  ... -0.254219   1.696351    -0.622145  -0.566305 -0.341379
9173    2.408319   1.441214        0.153816               0.374544    1.771806  ... -1.721634  -0.589501    -0.622145  -0.566305 -0.341379
...          ...        ...             ...                    ...         ...  ...       ...        ...          ...        ...       ...
12452  -0.415227   1.441214        0.102913               0.126276   -1.076980  ... -1.721634  -0.589501    -0.622145  -0.566305 -0.341379
12454  -0.415227   0.050866        0.388335              -0.038197   -1.076980  ...  0.584304  -0.589501     1.607342  -0.566305 -0.341379
12457  -0.415227   0.050866        3.108025               1.665530    0.347413  ... -1.721634  -0.589501    -0.622145  -0.566305 -0.341379
12458  -0.415227   0.050866        1.420944               1.113142    0.347413  ... -0.673480   1.696351    -0.622145  -0.566305 -0.341379
12459  -0.415227   1.441214        1.420944               0.459700    0.347413  ...  0.374674  -0.589501     1.607342  -0.566305 -0.341379

[928 rows x 33 columns], 9163     1
9164     1
9166     1
9167     1
9173     1
        ..
12452    1
12454    1
12457    1
12458    1
12459    1
Name: Target_Multiclass, Length: 928, dtype: int32)
test_data : (       D1_Bar_TL  H4_Bar_SW  H8_Price_Range  ADX_14_x_RollingVol15  H12_Bar_SW  ...      Hour  IsMorning  IsAfternoon  IsEvening   IsNight
12371  -0.415227   0.050866       -0.489747               0.325385   -1.076980  ... -0.673480   1.696351    -0.622145  -0.566305 -0.341379
12376  -0.415227   0.050866        0.255622               0.282996    0.347413  ... -1.931265  -0.589501    -0.622145  -0.566305  2.929293
12385  -0.415227   1.441214       -0.089793              -0.397301    1.771806  ...  0.374674  -0.589501     1.607342  -0.566305 -0.341379
12387   2.408319  -1.339481        0.224717              -0.041955   -1.076980  ... -1.931265  -0.589501    -0.622145  -0.566305  2.929293
12394  -0.415227   1.441214        1.757270               2.759247    0.347413  ...  1.422827  -0.589501    -0.622145   1.765834 -0.341379
...          ...        ...             ...                    ...         ...  ...       ...        ...          ...        ...       ...
16137  -0.415227   0.050866       -0.366125              -0.570871    0.347413  ... -0.254219   1.696351    -0.622145  -0.566305 -0.341379
16138  -0.415227   1.441214       -0.366125              -0.488955    0.347413  ...  0.374674  -0.589501     1.607342  -0.566305 -0.341379
16139  -0.415227  -1.339481        0.112002              -0.375545    0.347413  ...  0.793935  -0.589501    -0.622145   1.765834 -0.341379
16141  -0.415227   1.441214       -0.100701              -0.292347   -1.076980  ... -1.931265  -0.589501    -0.622145  -0.566305  2.929293
16143  -0.415227  -1.339481       -0.287952               0.012817   -1.076980  ...  0.165043  -0.589501     1.607342  -0.566305 -0.341379

[928 rows x 33 columns], 12371    0
12376    0
12385    0
12387    0
12394    0
        ..
16137    0
16138    0
16139    1
16141    1
16143    0
Name: Target_Multiclass, Length: 928, dtype: int32)
df :        D1_Bar_TL  H4_Bar_SW  H8_Price_Range  ADX_14_x_RollingVol15  H12_Bar_SW  ...  Hour  IsMorning  IsAfternoon  IsEvening  IsNight
12371        0.0        0.0         0.00293               0.039118        -1.0  ...     9          1            0          0        0
12376        0.0        0.0         0.00703               0.037745         0.0  ...     3          0            0          0        1
12385        0.0        1.0         0.00513               0.015706         1.0  ...    14          0            1          0        0
12387        1.0       -1.0         0.00686               0.027218        -1.0  ...     3          0            0          0        1
12394        0.0        1.0         0.01529               0.117967         0.0  ...    19          0            0          1        0
...          ...        ...             ...                    ...         ...  ...   ...        ...          ...        ...      ...
16137        0.0        0.0         0.00361               0.010083         0.0  ...    11          1            0          0        0
16138        0.0        1.0         0.00361               0.012737         0.0  ...    14          0            1          0        0
16139        0.0       -1.0         0.00624               0.016411         0.0  ...    16          0            0          1        0
16141        0.0        1.0         0.00507               0.019106        -1.0  ...     3          0            0          0        1
16143        0.0       -1.0         0.00404               0.028992        -1.0  ...    13          0            1          0        0

[928 rows x 33 columns]
trade_df :                Entry Time  Entry Price           Exit Time  Exit Price  ... Main_Target  Target_Buy  Target_Sell  Target_Multiclass
0     2013-07-23 04:00:00      1.53645 2013-07-23 07:00:00     1.53645  ...           0           0           -1                  3
1     2013-07-23 10:00:00      1.53618 2013-07-23 10:00:00     1.53618  ...           0           0           -1                  3
2     2013-07-23 13:00:00      1.53500 2013-07-23 13:00:00     1.53366  ...           0           0           -1                  2
3     2013-07-23 15:00:00      1.53514 2013-07-23 18:00:00     1.53514  ...           0           0           -1                  3
4     2013-07-23 19:00:00      1.53770 2013-07-23 21:00:00     1.53770  ...           0           0           -1                  3
...                   ...          ...                 ...         ...  ...         ...         ...          ...                ...
16139 2025-07-10 17:00:00      1.35522 2025-07-10 18:00:00     1.35522  ...           0          -1            0                  1
16140 2025-07-10 20:00:00      1.35633 2025-07-10 20:00:00     1.35747  ...           0          -1            0                  2
16141 2025-07-11 04:00:00      1.35540 2025-07-11 10:00:00     1.35540  ...           0          -1            0                  1
16142 2025-07-11 12:00:00      1.35348 2025-07-11 12:00:00     1.35540  ...           0          -1            0                  2
16143 2025-07-11 14:00:00      1.35289 2025-07-11 17:00:00     1.34874  ...           1          -1            1                  0

[16144 rows x 338 columns]
stats : {'buy': {'win_rate': 24.23, 'expectancy': -41.395}, 'sell': {'win_rate': 26.01, 'expectancy': -35.007}, 'buy_sell': {'win_rate': 25.09, 'expectancy': -38.299}}
features : 33
['D1_Bar_TL', 'H4_Bar_SW', 'H8_Price_Range', 'ADX_14_x_RollingVol15', 'H12_Bar_SW', 'MA_Cross_50_100', 'H4_Price_Range', 'H8_MACD_line', 'H4_MACD_deep', 'Price_Range']

✅ แสดงช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล (train/val/test)
Train: 2013-07-30 ถึง 2020-09-15 (2605 วัน, 2788 records)
Val: 2020-05-13 ถึง 2022-10-12 (883 วัน, 928 records)
Test: 2022-09-16 ถึง 2025-07-11 (1030 วัน, 928 records)

✅ ข้อมูล df หลังจาก load and process data
จำนวน columns ใน df: 33

✅ ข้อมูล trade_df หลังจาก load and process data
จำนวน columns ใน trade_df: 338
🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนวิเคราะห์ SL/TP + เวลา : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

🏗️ เปิดใช้งาน analyze sl tp performance
📊 สถิติการทำงานของ SL/TP:
==================================================
ประเภทการออก        จำนวน     อัตราส่วน 
--------------------------------------------------
TP Hit              2165      13.41%
SL Hit              13852     85.80%
Technical Exit      127       0.79%
SL + Tech Exit      13979     86.59%
==================================================
กำไรเฉลี่ยเมื่อ TP Hit: 382.98
ขาดทุนเฉลี่ยเมื่อ SL Hit: -83.14
กำไร/ขาดทุนเฉลี่ยเมื่อออกด้วยสัญญาณเทคนิค: -80.57
อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:4.61

ผลรวม SL + Technical Exit:
- จำนวนเทรดรวม: 13979
- อัตราส่วน: 86.59%
- กำไร/ขาดทุนเฉลี่ย: -83.12
- อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:4.61

การออกด้วยสัญญาณเทคนิค:
- กำไรเฉลี่ยเมื่อชน TP: 29.07
- ขาดทุนเฉลี่ยเมื่อชน SL: -95.25
- อัตราการชน TP: 11.81%
- อัตราการชน SL: 88.19%

🏗️ เปิดใช้งาน analyze time performance
📊 ประสิทธิภาพตามวันในสัปดาห์:
          Profit                        Target
           count       mean      sum      mean
DayOfWeek                                     
Monday      3014 -24.893829 -75030.0  0.131387
Tuesday     3334 -16.929814 -56444.0  0.143071
Wednesday   3290 -24.754103 -81441.0  0.125836
Thursday    3341 -16.450165 -54960.0  0.138581
Friday      3165 -20.496367 -64871.0  0.131122

📊 ประสิทธิภาพตามชั่วโมง:
     Profit                        Target
      count       mean      sum      mean
Hour                                     
0         5   0.000000      0.0  0.000000
3      1075 -25.049302 -26928.0  0.128372
4       712 -29.558989 -21046.0  0.123596
5       525 -14.009524  -7355.0  0.161905
6       436 -18.552752  -8089.0  0.130734
7       423 -28.297872 -11970.0  0.139480
8       551 -11.803993  -6504.0  0.168784
9       975 -25.115897 -24488.0  0.130256
10     1138 -11.539543 -13132.0  0.137083
11     1228 -19.055375 -23400.0  0.124593
12     1118 -15.441860 -17264.0  0.143113
13     1044 -19.147510 -19990.0  0.133142
14     1047 -11.333333 -11866.0  0.146132
15     1166 -12.218696 -14247.0  0.136364
16     1164 -11.936426 -13894.0  0.162371
17     1160 -39.521552 -45845.0  0.098276
18      944 -21.967161 -20737.0  0.132415
19      728 -30.537088 -22231.0  0.126374
20      704 -33.750000 -23760.0  0.110795
23        1   0.000000      0.0  0.000000
💾 บันทึกกราฟวิเคราะห์เวลา ที่: LightGBM/Multi/results\M60_GBPUSD_time_analysis.png

============================================================
🤖 ขั้นตอนที่ 2: เทรนโมเดล LightGBM
============================================================
🔧 โหมดการทำงาน: Development
🔧 เทรนโมเดลใหม่: ใช่
🔧 ทดสอบ Optimal Parameters: ใช่
============================================================
🔄 ใช้ Multi-Model Architecture (2 โมเดลแยกตามสถานการณ์)

🔄 เทรนโมเดลใหม่ (TRAIN_NEW_MODEL = True)
🔍 Debug: เตรียม merge df และ trade_df
🔍 Debug: df.shape = (928, 33), trade_df.shape = (16144, 338)
🔍 ตรวจสอบคอลัมน์ที่จำเป็นใน combined_df...
⚠️ ไม่พบคอลัมน์ที่จำเป็น: ['Close', 'High', 'Low', 'EMA200']
🔧 พยายามเพิ่มจาก df_with_features...
✅ เพิ่มคอลัมน์ Close จาก df_with_features
✅ เพิ่มคอลัมน์ High จาก df_with_features
✅ เพิ่มคอลัมน์ Low จาก df_with_features
✅ เพิ่มคอลัมน์ EMA200 จาก df_with_features
✅ เพิ่มคอลัมน์ที่จำเป็นครบถ้วนแล้ว
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_09a_combined_df.csv

🔍 ตรวจสอบ columns ข้อมูล combined_df ขั้นตอนเทรน : จำนวน 37
['D1_Bar_TL', 'H4_Bar_SW', 'H8_Price_Range', 'ADX_14_x_RollingVol15', 'H12_Bar_SW', 'MA_Cross_50_100', 'H4_Price_Range', 'H8_MACD_line', 'H4_MACD_deep', 'Price_Range', 'RSI_signal_EMA12', 'H8_Price_Move', 'Volume_Momentum', 'Bar_TL', 'H12_Bar_OSB', 'Volume_Change_2', 'Bar_SW', 'D1_Bar_longwick', 'H4_Bar_longwick', 'RSI14_x_Volume', 'RSI_ROC_i4', 'Bar_CL_OC', 'Volume_Lag_50', 'Volume_Change_3', 'MA_Cross_50_200', 'H8_MACD_signal', 'ATR_ROC_i2', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Close', 'High', 'Low', 'EMA200']

🚀 ใช้ Logic การ Merge ใหม่โดยอ้างอิงจาก Timestamp ที่ถูกต้อง
🔍 เตรียม Merge ข้อมูล 10 คอลัมน์จาก trade_df
🔍 ตรวจสอบคอลัมน์ DateTime ใน combined_df
   Columns ใน combined_df: ['D1_Bar_TL', 'H4_Bar_SW', 'H8_Price_Range', 'ADX_14_x_RollingVol15', 'H12_Bar_SW', 'MA_Cross_50_100', 'H4_Price_Range', 'H8_MACD_line', 'H4_MACD_deep', 'Price_Range', 'RSI_signal_EMA12', 'H8_Price_Move', 'Volume_Momentum', 'Bar_TL', 'H12_Bar_OSB', 'Volume_Change_2', 'Bar_SW', 'D1_Bar_longwick', 'H4_Bar_longwick', 'RSI14_x_Volume', 'RSI_ROC_i4', 'Bar_CL_OC', 'Volume_Lag_50', 'Volume_Change_3', 'MA_Cross_50_200', 'H8_MACD_signal', 'ATR_ROC_i2', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Close', 'High', 'Low', 'EMA200']
⚠️ ไม่พบคอลัมน์ 'DateTime' ใน combined_df - กำลังสร้างใหม่
🔄 ใช้ fallback method: สร้าง DateTime sequence
✅ สร้าง DateTime sequence จาก 2025-08-24 01:43:24.347067 ถึง 2025-10-01 16:43:24.347067
✅ แปลงและเรียงลำดับ DateTime สำเร็จ (ช่วงเวลา: 2025-08-24 01:43:24.347067 ถึง 2025-10-01 16:43:24.347067)
✅ คำนวณ Timeframe จากข้อมูลได้: 0 days 01:00:00
✅ Merge สำเร็จ พบข้อมูลที่ตรงกัน 0 รายการ
✅ จัดการค่าว่างหลังการ Merge...
✅ เติมค่าว่างตามที่กำหนดเรียบร้อยแล้ว
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_09b_combined_df_merge.csv

🔍 ตรวจสอบ columns ข้อมูล combined_df ขั้นตอนเทรน : จำนวน 48
['D1_Bar_TL', 'H4_Bar_SW', 'H8_Price_Range', 'ADX_14_x_RollingVol15', 'H12_Bar_SW', 'MA_Cross_50_100', 'H4_Price_Range', 'H8_MACD_line', 'H4_MACD_deep', 'Price_Range', 'RSI_signal_EMA12', 'H8_Price_Move', 'Volume_Momentum', 'Bar_TL', 'H12_Bar_OSB', 'Volume_Change_2', 'Bar_SW', 'D1_Bar_longwick', 'H4_Bar_longwick', 'RSI14_x_Volume', 'RSI_ROC_i4', 'Bar_CL_OC', 'Volume_Lag_50', 'Volume_Change_3', 'MA_Cross_50_200', 'H8_MACD_signal', 'ATR_ROC_i2', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Close', 'High', 'Low', 'EMA200', 'DateTime', 'Entry Time', 'Entry Price', 'Exit Price', 'Trade Type', 'Profit', 'Exit Condition', 'Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']
features : 33
['D1_Bar_TL', 'H4_Bar_SW', 'H8_Price_Range', 'ADX_14_x_RollingVol15', 'H12_Bar_SW', 'MA_Cross_50_100', 'H4_Price_Range', 'H8_MACD_line', 'H4_MACD_deep', 'Price_Range']
✅ ใช้ Final selected feature: 33 feature

🏗️ เปิดใช้งาน train all scenario models
🚀 เริ่มเทรนโมเดลทั้ง 2 scenarios สำหรับ GBPUSD_M60
============================================================
📁 ใช้ results_dir: LightGBM/Multi/results
🔍 Debug: USE_MULTICLASS_TARGET = True
🔍 Debug: target_column = Target_Multiclass
✅ สร้างโฟลเดอร์ทั้งหมดเรียบร้อยแล้ว
🔍 Debug: ตรวจสอบคอลัมน์ใน df ก่อนเพิ่ม market_scenario
🔍 Debug: df.columns[:10] = ['D1_Bar_TL', 'H4_Bar_SW', 'H8_Price_Range', 'ADX_14_x_RollingVol15', 'H12_Bar_SW', 'MA_Cross_50_100', 'H4_Price_Range', 'H8_MACD_line', 'H4_MACD_deep', 'Price_Range']
🔍 Debug: มีคอลัมน์ Close? True
🔍 Debug: มีคอลัมน์ EMA200? True
✅ พบคอลัมน์ที่จำเป็นครบถ้วน

🏗️ เปิดใช้งาน add market scenario column

📈 Market Scenario Distribution:
  downtrend: 484 (52.2%)
  uptrend: 386 (41.6%)
  sideways: 58 (6.2%)
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_10a_df_with_scenario.csv

================================================================================
📊 กำลังเทรน trend_following...
================================================================================
🔍 Debug: เตรียมข้อมูลสำหรับ trend_following

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Multiclass

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 928 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 928/928 rows (100.0%)
📊 ข้อมูลหลังกรอง trend_following: 928 samples

🛠️ Features used for training (Selected: 33 total):
['D1_Bar_TL', 'H4_Bar_SW', 'H8_Price_Range', 'ADX_14_x_RollingVol15', 'H12_Bar_SW', 'MA_Cross_50_100', 'H4_Price_Range', 'H8_MACD_line', 'H4_MACD_deep', 'Price_Range', 'RSI_signal_EMA12', 'H8_Price_Move', 'Volume_Momentum', 'Bar_TL', 'H12_Bar_OSB', 'Volume_Change_2', 'Bar_SW', 'D1_Bar_longwick', 'H4_Bar_longwick', 'RSI14_x_Volume', 'RSI_ROC_i4', 'Bar_CL_OC', 'Volume_Lag_50', 'Volume_Change_3', 'MA_Cross_50_200', 'H8_MACD_signal', 'ATR_ROC_i2', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']
📊 Class distribution สำหรับ trend_following: {0.0: 928}
✅ เตรียมข้อมูล trend_following: 928 samples, 33 features

✅ ข้อมูลพร้อม: X.shape=(928, 33), y.shape=(928,)
🔍 Debug: เรียกใช้ train scenario model() สำหรับ trend_following

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following สำหรับ GBPUSD_M60
📊 ข้อมูล: 928 samples, 33 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following สำหรับ GBPUSD_M60
📊 ข้อมูล: 928 samples, 33 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
⚠️ มี class เดียว (1) ไม่สามารถเทรนได้
❌ เทรน trend_following ล้มเหลว - result เป็น None

================================================================================
📊 กำลังเทรน counter_trend...
================================================================================
🔍 Debug: เตรียมข้อมูลสำหรับ counter_trend

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Multiclass

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 928 rows
📊 Counter Trend Strategy (Mean Reversion ใน Sideways/Consolidation): 919/928 rows (99.0%)
📊 ข้อมูลหลังกรอง counter_trend: 919 samples

🛠️ Features used for training (Selected: 33 total):
['D1_Bar_TL', 'H4_Bar_SW', 'H8_Price_Range', 'ADX_14_x_RollingVol15', 'H12_Bar_SW', 'MA_Cross_50_100', 'H4_Price_Range', 'H8_MACD_line', 'H4_MACD_deep', 'Price_Range', 'RSI_signal_EMA12', 'H8_Price_Move', 'Volume_Momentum', 'Bar_TL', 'H12_Bar_OSB', 'Volume_Change_2', 'Bar_SW', 'D1_Bar_longwick', 'H4_Bar_longwick', 'RSI14_x_Volume', 'RSI_ROC_i4', 'Bar_CL_OC', 'Volume_Lag_50', 'Volume_Change_3', 'MA_Cross_50_200', 'H8_MACD_signal', 'ATR_ROC_i2', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']
📊 Class distribution สำหรับ counter_trend: {0.0: 919}
✅ เตรียมข้อมูล counter_trend: 919 samples, 33 features

✅ ข้อมูลพร้อม: X.shape=(919, 33), y.shape=(919,)
🔍 Debug: เรียกใช้ train scenario model() สำหรับ counter_trend

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend สำหรับ GBPUSD_M60
📊 ข้อมูล: 919 samples, 33 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend สำหรับ GBPUSD_M60
📊 ข้อมูล: 919 samples, 33 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
⚠️ มี class เดียว (1) ไม่สามารถเทรนได้
❌ เทรน counter_trend ล้มเหลว - result เป็น None
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GBPUSD_10b_df_with_scenario.csv

🔍 Debug: ตรวจสอบ Target columns ที่มีอยู่:
   Target_Buy: 928/928 samples (100.0%)
   Target_Sell: 928/928 samples (100.0%)

================================================================================
📊 กำลังเทรน Target_Buy...
================================================================================
🔍 Debug: ข้อมูลสำหรับ Target_Buy: 928/928 rows

================================================================================
📊 กำลังเทรน trend_following สำหรับ Target_Buy...
================================================================================

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Buy

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 928 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 928/928 rows (100.0%)
📊 ข้อมูลหลังกรอง trend_following: 928 samples

🛠️ Features used for training (Selected: 33 total):
['D1_Bar_TL', 'H4_Bar_SW', 'H8_Price_Range', 'ADX_14_x_RollingVol15', 'H12_Bar_SW', 'MA_Cross_50_100', 'H4_Price_Range', 'H8_MACD_line', 'H4_MACD_deep', 'Price_Range', 'RSI_signal_EMA12', 'H8_Price_Move', 'Volume_Momentum', 'Bar_TL', 'H12_Bar_OSB', 'Volume_Change_2', 'Bar_SW', 'D1_Bar_longwick', 'H4_Bar_longwick', 'RSI14_x_Volume', 'RSI_ROC_i4', 'Bar_CL_OC', 'Volume_Lag_50', 'Volume_Change_3', 'MA_Cross_50_200', 'H8_MACD_signal', 'ATR_ROC_i2', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']
📊 Class distribution สำหรับ trend_following: {0.0: 928}
✅ เตรียมข้อมูล trend_following: 928 samples, 33 features
✅ ข้อมูลพร้อมสำหรับ trend_following_Buy: X.shape=(928, 33), y.shape=(928,)
📊 Class distribution: {0.0: 928}

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following_Buy สำหรับ GBPUSD_M60
📊 ข้อมูล: 928 samples, 33 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following_Buy สำหรับ GBPUSD_M60
📊 ข้อมูล: 928 samples, 33 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
⚠️ มี class เดียว (1) ไม่สามารถเทรนได้
❌ เทรน trend_following_Buy ล้มเหลว - result เป็น None

================================================================================
📊 กำลังเทรน counter_trend สำหรับ Target_Buy...
================================================================================

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Buy

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 928 rows
📊 Counter Trend Strategy (Mean Reversion ใน Sideways/Consolidation): 919/928 rows (99.0%)
📊 ข้อมูลหลังกรอง counter_trend: 919 samples

🛠️ Features used for training (Selected: 33 total):
['D1_Bar_TL', 'H4_Bar_SW', 'H8_Price_Range', 'ADX_14_x_RollingVol15', 'H12_Bar_SW', 'MA_Cross_50_100', 'H4_Price_Range', 'H8_MACD_line', 'H4_MACD_deep', 'Price_Range', 'RSI_signal_EMA12', 'H8_Price_Move', 'Volume_Momentum', 'Bar_TL', 'H12_Bar_OSB', 'Volume_Change_2', 'Bar_SW', 'D1_Bar_longwick', 'H4_Bar_longwick', 'RSI14_x_Volume', 'RSI_ROC_i4', 'Bar_CL_OC', 'Volume_Lag_50', 'Volume_Change_3', 'MA_Cross_50_200', 'H8_MACD_signal', 'ATR_ROC_i2', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']
📊 Class distribution สำหรับ counter_trend: {0.0: 919}
✅ เตรียมข้อมูล counter_trend: 919 samples, 33 features
✅ ข้อมูลพร้อมสำหรับ counter_trend_Buy: X.shape=(919, 33), y.shape=(919,)
📊 Class distribution: {0.0: 919}

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend_Buy สำหรับ GBPUSD_M60
📊 ข้อมูล: 919 samples, 33 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend_Buy สำหรับ GBPUSD_M60
📊 ข้อมูล: 919 samples, 33 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
⚠️ มี class เดียว (1) ไม่สามารถเทรนได้
❌ เทรน counter_trend_Buy ล้มเหลว - result เป็น None

================================================================================
📊 กำลังเทรน Target_Sell...
================================================================================
🔍 Debug: ข้อมูลสำหรับ Target_Sell: 928/928 rows

================================================================================
📊 กำลังเทรน trend_following สำหรับ Target_Sell...
================================================================================

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Sell

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 928 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 928/928 rows (100.0%)
📊 ข้อมูลหลังกรอง trend_following: 928 samples

🛠️ Features used for training (Selected: 33 total):
['D1_Bar_TL', 'H4_Bar_SW', 'H8_Price_Range', 'ADX_14_x_RollingVol15', 'H12_Bar_SW', 'MA_Cross_50_100', 'H4_Price_Range', 'H8_MACD_line', 'H4_MACD_deep', 'Price_Range', 'RSI_signal_EMA12', 'H8_Price_Move', 'Volume_Momentum', 'Bar_TL', 'H12_Bar_OSB', 'Volume_Change_2', 'Bar_SW', 'D1_Bar_longwick', 'H4_Bar_longwick', 'RSI14_x_Volume', 'RSI_ROC_i4', 'Bar_CL_OC', 'Volume_Lag_50', 'Volume_Change_3', 'MA_Cross_50_200', 'H8_MACD_signal', 'ATR_ROC_i2', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']
📊 Class distribution สำหรับ trend_following: {0.0: 928}
✅ เตรียมข้อมูล trend_following: 928 samples, 33 features
✅ ข้อมูลพร้อมสำหรับ trend_following_Sell: X.shape=(928, 33), y.shape=(928,)
📊 Class distribution: {0.0: 928}

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following_Sell สำหรับ GBPUSD_M60
📊 ข้อมูล: 928 samples, 33 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following_Sell สำหรับ GBPUSD_M60
📊 ข้อมูล: 928 samples, 33 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
⚠️ มี class เดียว (1) ไม่สามารถเทรนได้
❌ เทรน trend_following_Sell ล้มเหลว - result เป็น None

================================================================================
📊 กำลังเทรน counter_trend สำหรับ Target_Sell...
================================================================================

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Sell

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 928 rows
📊 Counter Trend Strategy (Mean Reversion ใน Sideways/Consolidation): 919/928 rows (99.0%)
📊 ข้อมูลหลังกรอง counter_trend: 919 samples

🛠️ Features used for training (Selected: 33 total):
['D1_Bar_TL', 'H4_Bar_SW', 'H8_Price_Range', 'ADX_14_x_RollingVol15', 'H12_Bar_SW', 'MA_Cross_50_100', 'H4_Price_Range', 'H8_MACD_line', 'H4_MACD_deep', 'Price_Range', 'RSI_signal_EMA12', 'H8_Price_Move', 'Volume_Momentum', 'Bar_TL', 'H12_Bar_OSB', 'Volume_Change_2', 'Bar_SW', 'D1_Bar_longwick', 'H4_Bar_longwick', 'RSI14_x_Volume', 'RSI_ROC_i4', 'Bar_CL_OC', 'Volume_Lag_50', 'Volume_Change_3', 'MA_Cross_50_200', 'H8_MACD_signal', 'ATR_ROC_i2', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']
📊 Class distribution สำหรับ counter_trend: {0.0: 919}
✅ เตรียมข้อมูล counter_trend: 919 samples, 33 features
✅ ข้อมูลพร้อมสำหรับ counter_trend_Sell: X.shape=(919, 33), y.shape=(919,)
📊 Class distribution: {0.0: 919}

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend_Sell สำหรับ GBPUSD_M60
📊 ข้อมูล: 919 samples, 33 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend_Sell สำหรับ GBPUSD_M60
📊 ข้อมูล: 919 samples, 33 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
⚠️ มี class เดียว (1) ไม่สามารถเทรนได้
❌ เทรน counter_trend_Sell ล้มเหลว - result เป็น None

================================================================================

✅ เทรนเสร็จสิ้น: 0 โมเดล
================================================================================
🔍 Debug: ผลลัพธ์การเทรน:
⚠️ ไม่มีผลลัพธ์การเทรนใดๆ - ไม่สามารถสร้าง Combined Feature Importance ได้
⚠️ ไม่มี metrics จาก scenarios - ไม่สามารถสร้าง Performance Analysis ได้

🏗️ เริ่มบันทึกผลลัพธ์ลงระบบสรุป Multi-Model
🔍 Debug: จำนวน results = 0
📊 บันทึกสรุปเสร็จสิ้น: 0/0 scenarios
❌ Multi-Model training ล้มเหลว - ลองใช้ Single-Model แทน
🔄 กำลังเปลี่ยนเป็น Single-Model Architecture...

🏗️ เปิดใช้งาน train and evaluate
✅ ข้อมูลผ่านการตรวจสอบเบื้องต้น
   Train: 2788 samples, 33 features
   Val: 928 samples
   Test: 928 samples
🔍 ตรวจสอบ Temporal Dependence ในชุด Train/Val/Test

⚙️ กำลัง Scaling Features...
✅ Scaling Features เสร็จสิ้น

🔍 การตรวจสอบ Data Quality และ Class Imbalance
============================================================
Train class distribution: Target_Multiclass
1    2135
0     653
Name: count, dtype: int64
Train class ratio (0:1): 0.31
Minority class ratio: 0.234
✅ Class distribution is acceptable

Data Quality Check:
- Train samples: 2788
- Val samples: 928
- Test samples: 928
- Features: 33
✅ No missing values detected
✅ No infinite values detected
⚙️ กำลังทำ SMOTE balancing...
SMOTE: ใช้ k_neighbors=5 (min_class_count=653)
SMOTE: ก่อน balance 0=653, 1=2135 | หลัง balance 0=2135, 1=2135

🚀 กำลังสร้างโมเดล LGBMClassifier ใหม่

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0 1]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {1: 2135, 0: 653}
   Imbalance ratio: 3.3:1
   Enhanced class weight: {1: 1, 0: 6}
  🎯 Auto Class Weight: {1: 1, 0: 6}
Class Ratio (0:1): 1.00:1
📁 สร้างโฟลเดอร์สำหรับ tuning files: LightGBM/Hyper_Multi/M60_GBPUSD

🔍 ตรวจสอบสถานะ Hyperparameter Tuning:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_GBPUSD\M60_GBPUSD_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GBPUSD\M60_GBPUSD_best_params.json
   flag_file exists = True
   param_file exists = True

✅ โหลด best hyperparameters จากไฟล์ LightGBM/Hyper_Multi/M60_GBPUSD\M60_GBPUSD_best_params.json
   - Best AUC: nan
   - Tuning Date: 2025-10-01T09:26:12.591476
   - CV Method: TimeSeriesSplit
✅ สร้างโมเดล LGBMClassifier ใหม่สำเร็จ
⚙️ กำลัง Fit โมเดลหลัก...
Training until validation scores don't improve for 200 rounds
[100]	valid_0's auc: 0.538393	valid_0's binary_logloss: 0.964677	valid_0's binary_error: 0.701509
[200]	valid_0's auc: 0.537686	valid_0's binary_logloss: 0.848168	valid_0's binary_error: 0.577586
Early stopping, best iteration is:
[84]	valid_0's auc: 0.544109	valid_0's binary_logloss: 1.00088	valid_0's binary_error: 0.71875
✅ ฝึกโมเดลหลักสำเร็จ

🏗️ เปิดใช้งาน validate model quality

🔍 Model Quality Validation for GBPUSD M60:
  📊 Accuracy: 0.281 ❌ (min: 0.68)
  📈 AUC: 0.544 ❌ (min: 0.78)
  📋 Samples: 928 ✅ (min: 200)
  🎯 Overall: ❌ FAIL
  ⚠️ Model quality insufficient - consider:
    - Improve feature engineering or model parameters
    - Address class imbalance or add more discriminative features
⚠️ โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ - อาจส่งผลต่อ win rate
   แนะนำ: ปรับ parameters, เพิ่มข้อมูล, หรือปรับปรุง features

🏗️ เปิดใช้งาน find best threshold on val with Trading Metrics
📊 Prepared features for threshold optimization: 33 available, 0 filled with 0
🔍 calculate_advanced_trading_metrics: input shape = (319, 35)
   📊 Total trades: 319
   💰 Profit range: 0.00 to 841.00
   ✅ Advanced metrics calculated:
      📈 Sharpe Ratio: 0.443
      📉 Max Drawdown: 0.00
      💰 Profit Factor: 999.99
      🎯 Win Rate: 17.9%
      💡 Expectancy: 84.39
🔍 calculate_advanced_trading_metrics: input shape = (198, 35)
   📊 Total trades: 198
   💰 Profit range: 0.00 to 841.00
   ✅ Advanced metrics calculated:
      📈 Sharpe Ratio: 0.441
      📉 Max Drawdown: 0.00
      💰 Profit Factor: 999.99
      🎯 Win Rate: 17.7%
      💡 Expectancy: 84.93
🔍 calculate_advanced_trading_metrics: input shape = (117, 35)
   📊 Total trades: 117
   💰 Profit range: 0.00 to 841.00
   ✅ Advanced metrics calculated:
      📈 Sharpe Ratio: 0.460
      📉 Max Drawdown: 0.00
      💰 Profit Factor: 999.99
      🎯 Win Rate: 18.8%
      💡 Expectancy: 90.31
Threshold grid search with Trading Metrics (val set):
  Threshold | Win Rate | Expectancy | Trades | Sharpe | Max DD | PF | Kelly | Score
  -------------------------------------------------------------------------------------
      0.35 |   17.9% |     84.39 |    319 |  0.443 |    0.0 | 999.99 | 0.179 | 2035.53
      0.40 |   17.7% |     84.93 |    198 |  0.441 |    0.0 | 999.99 | 0.177 | 2035.71
      0.45 |   18.8% |     90.31 |    117 |  0.460 |    0.0 | 999.99 | 0.188 | 2038.07

✅ เลือก threshold ที่ดีที่สุดบน validation set: 0.45
   📊 Composite Score: 2038.07
   📈 Win Rate: 18.8%, Expectancy: 90.31, Trades: 117
   📉 Sharpe: 0.460, Max DD: 0.0, PF: 999.99, Kelly: 0.188
🏗️ เปิดใช้งาน save optimal threshold (backward compatibility)
🏗️ เปิดใช้งาน save scenario threshold
✅ บันทึก threshold สำเร็จ: LightGBM/Multi/thresholds/GBPUSD_MM60_trend_following_threshold.json
   🎯 Threshold: 0.4500
🏗️ เปิดใช้งาน save scenario threshold
✅ บันทึก threshold สำเร็จ: LightGBM/Multi/thresholds/GBPUSD_MM60_counter_trend_threshold.json
   🎯 Threshold: 0.4500
-> เสร็จสิ้นการ Fit โมเดล LightGBM หลัก
-> กำลังเรียกตรวจสอบ features ของโมเดลหลัก
-> เสร็จสิ้นตรวจสอบ features

==================================================
  การทดสอบเปรียบเทียบกับ RandomForest 
==================================================
-> กำลังเรียก test random forest

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
              Feature  Importance
           ATR_ROC_i2    0.069552
          Price_Range    0.065921
           RSI_ROC_i4    0.065169
      Volume_Change_2    0.062899
       RSI14_x_Volume    0.062310
      D1_Bar_longwick    0.061525
ADX_14_x_RollingVol15    0.061506
      H4_Bar_longwick    0.061348
        H8_Price_Move    0.059839
      Volume_Change_3    0.059737
        Volume_Lag_50    0.059296
       H4_Price_Range    0.058950
       H8_Price_Range    0.058087
                 Hour    0.030880
            DayOfWeek    0.024497

📈 RandomForest Performance:
              precision    recall  f1-score   support

           0       0.33      0.00      0.01       217
           1       0.77      1.00      0.87       711

    accuracy                           0.77       928
   macro avg       0.55      0.50      0.44       928
weighted avg       0.67      0.77      0.67       928

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GBPUSD_random_forest_feature_importance.csv
-> เสร็จสิ้น test random forest

📊 การกระจายของคลาส:
Train - 0: 653, 1: 2135
Test - 0: 217, 1: 711

🔍 เริ่มทำ Cross-Validation...
-> กำลังเรียก time series cv

🏗️ เปิดใช้งาน time series cv
🔁 เริ่มทำ Time Series Cross-Validation (n_splits=5, original=5)
📊 CV Configuration: splits=5, test_size=619, total_samples=3716

📊 Fold 1/5:
  - Train size: 621 ตัวอย่าง (16.7% ของข้อมูลทั้งหมด)
  - Val size:   619 ตัวอย่าง
  - การกระจายคลาสใน Train: {1: 0.7632850241545893, 0: 0.23671497584541062}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0 1]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {1: 474, 0: 147}
   Imbalance ratio: 3.2:1
   Enhanced class weight: {1: 1, 0: 6}
  🎯 Auto Class Weight: {1: 1, 0: 6}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 1 trees)
  📊 ผลลัพธ์ Fold 1:
    - Accuracy:  0.2052
    - AUC:       0.5715
    - F1 Score:  0.0000
    - Precision: 0.0000
    - Recall:    0.0000

📊 Fold 2/5:
  - Train size: 1240 ตัวอย่าง (33.4% ของข้อมูลทั้งหมด)
  - Val size:   619 ตัวอย่าง
  - การกระจายคลาสใน Train: {1: 0.7790322580645161, 0: 0.22096774193548388}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0 1]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {1: 966, 0: 274}
   Imbalance ratio: 3.5:1
   Enhanced class weight: {1: 1, 0: 6}
  🎯 Auto Class Weight: {1: 1, 0: 6}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 1 trees)
  📊 ผลลัพธ์ Fold 2:
    - Accuracy:  0.2294
    - AUC:       0.5206
    - F1 Score:  0.0000
    - Precision: 0.0000
    - Recall:    0.0000

📊 Fold 3/5:
  - Train size: 1859 ตัวอย่าง (50.0% ของข้อมูลทั้งหมด)
  - Val size:   619 ตัวอย่าง
  - การกระจายคลาสใน Train: {1: 0.7762237762237763, 0: 0.22377622377622378}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0 1]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {1: 1443, 0: 416}
   Imbalance ratio: 3.5:1
   Enhanced class weight: {1: 1, 0: 6}
  🎯 Auto Class Weight: {1: 1, 0: 6}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 1 trees)
  📊 ผลลัพธ์ Fold 3:
    - Accuracy:  0.2375
    - AUC:       0.5242
    - F1 Score:  0.0000
    - Precision: 0.0000
    - Recall:    0.0000

📊 Fold 4/5:
  - Train size: 2478 ตัวอย่าง (66.7% ของข้อมูลทั้งหมด)
  - Val size:   619 ตัวอย่าง
  - การกระจายคลาสใน Train: {1: 0.7728006456820016, 0: 0.2271993543179984}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0 1]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {1: 1915, 0: 563}
   Imbalance ratio: 3.4:1
   Enhanced class weight: {1: 1, 0: 6}
  🎯 Auto Class Weight: {1: 1, 0: 6}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 1 trees)
  📊 ผลลัพธ์ Fold 4:
    - Accuracy:  0.2456
    - AUC:       0.5126
    - F1 Score:  0.0000
    - Precision: 0.0000
    - Recall:    0.0000

📊 Fold 5/5:
  - Train size: 3097 ตัวอย่าง (83.3% ของข้อมูลทั้งหมด)
  - Val size:   619 ตัวอย่าง
  - การกระจายคลาสใน Train: {1: 0.7691314175008073, 0: 0.23086858249919276}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0 1]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {1: 2382, 0: 715}
   Imbalance ratio: 3.3:1
   Enhanced class weight: {1: 1, 0: 6}
  🎯 Auto Class Weight: {1: 1, 0: 6}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 1 trees)
  📊 ผลลัพธ์ Fold 5:
    - Accuracy:  0.2504
    - AUC:       0.5013
    - F1 Score:  0.0000
    - Precision: 0.0000
    - Recall:    0.0000

📊 สรุปการทำ Time Series CV:
   - จำนวน folds ที่วางแผน: 5
   - จำนวน folds ที่สำเร็จ: 5

✅ การทำ Time Series Cross-Validation เสร็จสมบูรณ์
📌 ผลลัพธ์เฉลี่ย:
  - Accuracy:  0.2336
  - AUC:       0.5260
  - F1 Score:  0.0000
  - Precision: 0.0000
  - Recall:    0.0000
-> เสร็จสิ้น time series cv

  การประเมินผลโมเดลแบบละเอียด symbol GBPUSD timeframe M60

🏗️ เปิดใช้งาน enhanced evaluation
🔍 เริ่มการประเมินผลโมเดลแบบละเอียด symbol GBPUSD timeframe M60...
📁 สร้างโฟลเดอร์สำหรับ timeframe symbol GBPUSD timeframe M60 ที่: LightGBM/Multi/results\M60_GBPUSD
📊 Binary Classification

Classification Report (Binary Target):
              precision    recall  f1-score     support
0              0.234539  0.926267  0.374302  217.000000
1              0.774648  0.077356  0.140665  711.000000
accuracy       0.275862  0.275862  0.275862    0.275862
macro avg      0.504593  0.501812  0.257483  928.000000
weighted avg   0.648351  0.275862  0.195298  928.000000
📝 กำลังคำนวณ metrics...
📊 กำลังสร้าง visualization...
✅ บันทึกกราฟประสิทธิภาพที่: LightGBM/Multi/results\M60_GBPUSD\M60_GBPUSD_performance_curves.png
✅ บันทึกรายงานการประเมินที่: LightGBM/Multi/results\M60_GBPUSD\M60_GBPUSD_evaluation_report.csv
🎯 การประเมินผลสำหรับ symbol GBPUSD timeframe M60 เสร็จสมบูรณ์!

📊 ผลการประเมินแบบละเอียด symbol GBPUSD timeframe M60
AUC-ROC: 0.5642
AUC-PR: 0.8040
              precision    recall  f1-score     support
0              0.234539  0.926267  0.374302  217.000000
1              0.774648  0.077356  0.140665  711.000000
accuracy       0.275862  0.275862  0.275862    0.275862
macro avg      0.504593  0.501812  0.257483  928.000000
weighted avg   0.648351  0.275862  0.195298  928.000000

📌 สรุปผลลัพธ์แบบละเอียด:
- Timeframe: M60
- Accuracy: 0.2759
- Auc_roc: 0.5642
- Auc_pr: 0.8040
- F1: 0.2575
- Precision: 0.5046
- Recall: 0.5018

กำลังบันทึกโมเดลที่: LightGBM/Multi/models/M60_GBPUSD\M60_GBPUSD_trained.pkl
✅ บันทึกโมเดลเรียบร้อย (ขนาด: 296.45 KB)

กำลังบันทึก features ที่: LightGBM/Multi/models/M60_GBPUSD\M60_GBPUSD_features.pkl
✅ บันทึก features เรียบร้อย (จำนวน features: 33)

กำลังบันทึก Scaler ที่: LightGBM/Multi/models/M60_GBPUSD\M60_GBPUSD_scaler.pkl
✅ บันทึก Scaler เรียบร้อย (ขนาด: 2.25 KB)

📝 บันทึกประวัติการเทรนที่: LightGBM/Multi/training_history\M60_GBPUSD_training_history.csv
-> กำลังสร้าง Feature Importance
✅ พิมพ์ feature ก่อนส่งเข้า plot feature importance
['D1_Bar_TL', 'H4_Bar_SW', 'H8_Price_Range', 'ADX_14_x_RollingVol15', 'H12_Bar_SW', 'MA_Cross_50_100', 'H4_Price_Range', 'H8_MACD_line', 'H4_MACD_deep', 'Price_Range', 'RSI_signal_EMA12', 'H8_Price_Move', 'Volume_Momentum', 'Bar_TL', 'H12_Bar_OSB', 'Volume_Change_2', 'Bar_SW', 'D1_Bar_longwick', 'H4_Bar_longwick', 'RSI14_x_Volume', 'RSI_ROC_i4', 'Bar_CL_OC', 'Volume_Lag_50', 'Volume_Change_3', 'MA_Cross_50_200', 'H8_MACD_signal', 'ATR_ROC_i2', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ LightGBM_Single symbol GBPUSD timeframe M60

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
        Feature   Gain  Split
      DayOfWeek 0.1918 0.1010
      H4_Bar_SW 0.1307 0.0686
    Price_Range 0.0432 0.0489
 RSI14_x_Volume 0.0431 0.0571
Volume_Change_3 0.0408 0.0517
           Hour 0.0401 0.0460
H4_Bar_longwick 0.0392 0.0538
     ATR_ROC_i2 0.0373 0.0431
 H4_Price_Range 0.0370 0.0493
D1_Bar_longwick 0.0355 0.0501
     H12_Bar_SW 0.0353 0.0353
     RSI_ROC_i4 0.0333 0.0435
 H8_MACD_signal 0.0322 0.0304
         Bar_SW 0.0321 0.0226
  H8_Price_Move 0.0319 0.0411

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GBPUSD_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GBPUSD_feature_importance.csv (ขนาด: 1841 bytes)
-> เสร็จสิ้น Feature Importance

🔍 Top 5 Most Important Features (Gain):
DayOfWeek: 0.1918 (Gain), 0.1010 (Split)
H4_Bar_SW: 0.1307 (Gain), 0.0686 (Split)
Price_Range: 0.0432 (Gain), 0.0489 (Split)
RSI14_x_Volume: 0.0431 (Gain), 0.0571 (Split)
Volume_Change_3: 0.0408 (Gain), 0.0517 (Split)

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GBPUSD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GBPUSD_feature_importance_comparison.csv

📊 เปรียบเทียบผลลัพธ์:
| Metric      | CV Avg    | Test Set |
|-------------|-----------|----------|
| Accuracy    | 0.2336    | 0.2759 |
| AUC         | 0.5260    | 0.5642 |
| F1 Score    | 0.0000    | 0.2575 |
-> train and evaluate ทำงานเสร็จสิ้น

🏗️ เปิดใช้งาน validate model quality

🔍 Model Quality Validation for GBPUSD M60:
  📊 Accuracy: 0.281 ❌ (min: 0.68)
  📈 AUC: 0.544 ❌ (min: 0.78)
  📋 Samples: 928 ✅ (min: 200)
  🎯 Overall: ❌ FAIL
  ⚠️ Model quality insufficient - consider:
    - Improve feature engineering or model parameters
    - Address class imbalance or add more discriminative features
⚠️ โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ แต่จะคืนค่าผลลัพธ์ต่อไป
✅ คืนค่าผลลัพธ์สำเร็จ: model=LGBMClassifier, scaler=StandardScaler

🏗️ เริ่มบันทึกผลลัพธ์ลงระบบสรุป Single-Model
🔍 Debug: trade_df is not None: True
🔍 Debug: trade_df.empty: False
🔍 Debug: trade_df length: 16144
✅ กำลังบันทึกสรุปสำหรับ Single-Model
   📊 Test stats: {'buy': {'count': 8393, 'win_rate': 12.93, 'expectancy': -22.09}, 'sell': {'count': 7751, 'win_rate': 14.13, 'expectancy': -19.01}, 'buy_sell': {'count': 16144, 'win_rate': 13.5, 'expectancy': -20.61}}
   📊 Train+Val stats: {'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'buy_sell': {'count': 928, 'win_rate': 0.0, 'expectancy': 0.0}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 33, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=GBPUSD, timeframe=M60, scenario=None
🔍 Debug: train_val_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'buy_sell': {'count': 928, 'win_rate': 0.0, 'expectancy': 0.0}}
🔍 Debug: test_stats={'buy': {'count': 8393, 'win_rate': 12.93, 'expectancy': -22.09}, 'sell': {'count': 7751, 'win_rate': 14.13, 'expectancy': -19.01}, 'buy_sell': {'count': 16144, 'win_rate': 13.5, 'expectancy': -20.61}}
🔍 Debug: model_metrics={'timeframe': 'M60', 'accuracy': 0.27586206896551724, 'auc': 0.5641952983725136, 'f1': 0.2574833188072411, 'precision': 0.5045934885861258, 'recall': 0.5018115589777492, 'confusion_matrix': array([[201,  16],
       [656,  55]], dtype=int64), 'auc_pr': 0.8040437456024068}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 33, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 2, คะแนนเฉลี่ย: 57.2/100
📈 Win Rate เฉลี่ย: 38.3%, Expectancy เฉลี่ย: -7.79
🏆 โมเดลที่ดีที่สุด: GOLD M060 (Score: 80.1)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน GBPUSD MM60 เรียบร้อย
📊 Performance Score: 34.33
🔍 Debug: กำลังเตรียมข้อมูลสำหรับบันทึกผลลัพธ์การเปรียบเทียบ
   - Current Entry Config: config_1_enhanced_signal
   - Symbol: GBPUSD, Timeframe: M60
   - Test stats keys: ['buy', 'sell', 'buy_sell']
   - Buy_sell stats: {'count': 16144, 'win_rate': 13.5, 'expectancy': -20.61}
   - Performance data prepared: {'symbol': 'GBPUSD', 'timeframe': 'M60', 'entry_config': 'config_1_enhanced_signal', 'entry_config_name': 'Enhanced MACD Signal', 'entry_config_description': 'ใช้ macd_signal พร้อมเงื่อนไข volume และ pullback เพิ่มเติม', 'win_rate': 0.135, 'expectancy': -20.61, 'profit_factor': 0, 'num_trades': 16144, 'max_drawdown': 0, 'model_accuracy': 0.27586206896551724, 'model_auc': 0.5641952983725136, 'model_f1': 0.2574833188072411, 'training_date': '2025-10-01T16:43:31.028807', 'num_features': 33}

🏗️ เปิดใช้งาน save entry config performance

🏗️ เปิดใช้งาน get entry config results folder

🏗️ เปิดใช้งาน get entry config folder name
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models\M60_GBPUSD
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results\M60_GBPUSD
path บันทึกไฟล์ performance_summary.json LightGBM/Entry_config_1_enhanced_signal\results\M60_GBPUSD
✅ บันทึกผลการประเมิน config_1_enhanced_signal สำหรับ GBPUSD MM60 ที่: LightGBM/Entry_config_1_enhanced_signal\results\M60_GBPUSD\performance_summary.json
   📁 ขนาดไฟล์: 694 bytes
✅ บันทึกผลลัพธ์การเปรียบเทียบ Entry Config เสร็จสิ้น
✅ บันทึกสรุปสำหรับ Single-Model เรียบร้อย
✅ Single-Model fallback สำเร็จ

🎯 ทดสอบ Optimal Parameters สำหรับ Single-Model...
⚠️ ใช้ส่วนท้ายของ combined_df สำหรับ Single-Model validation
🎯 ทดสอบ Optimal Threshold...

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
⚠️ ไม่พบไฟล์ threshold สำหรับ GBPUSD MM60, ใช้ค่า default: 0.25
🏗️ เปิดใช้งาน save optimal threshold (backward compatibility)
🏗️ เปิดใช้งาน save scenario threshold
✅ บันทึก threshold สำเร็จ: LightGBM/Multi/thresholds/GBPUSD_MM60_trend_following_threshold.json
   🎯 Threshold: 0.2500
🏗️ เปิดใช้งาน save scenario threshold
✅ บันทึก threshold สำเร็จ: LightGBM/Multi/thresholds/GBPUSD_MM60_counter_trend_threshold.json
   🎯 Threshold: 0.2500
✅ Optimal Threshold: 0.2500
🎯 ทดสอบ Optimal nBars SL...

🏗️ เปิดใช้งาน find optimal nbars sl
🔍 Validation set info: 185 rows, columns: ['D1_Bar_TL', 'H4_Bar_SW', 'H8_Price_Range', 'ADX_14_x_RollingVol15', 'H12_Bar_SW', 'MA_Cross_50_100', 'H4_Price_Range', 'H8_MACD_line', 'H4_MACD_deep', 'Price_Range']...
🔍 Sample data range: 743 to 927
🔍 Entry function: single_model
🔍 Entry function type: <class 'NoneType'>
⚠️ Validation set ขาดคอลัมน์ที่จำเป็น: ['Open', 'EMA50', 'MACD_signal', 'RSI14', 'Volume', 'Volume_MA_20', 'PullBack_50_Up', 'Ratio_Buy', 'PullBack_50_Down', 'Ratio_Sell', 'ATR']
⚠️ จะใช้ค่า optimal nBars SL เดิมหรือ default

🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ trend_following, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M60_GBPUSD_trend_following_optimal_nBars_SL.pkl')
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ counter_trend, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M60_GBPUSD_counter_trend_optimal_nBars_SL.pkl')
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ GBPUSD MM60, ใช้ค่า default: 4
✅ Optimal nBars SL: 4

✅ ข้อมูล df และ trade_df หลังจาก train and evaluate
จำนวน columns ใน df: 33
จำนวน columns ใน trade_df: 338

[INFO] จำนวน Features หลัง train and evaluate: 33
[INFO] รายชื่อ Features หลัง train (แสดง 10 ตัวแรก): [27, 1, 9, 19, 23, 28, 18, 26, 6, 17]
[INFO] ... และอีก 23 features
🔍 Debug ไฟล์ CSV_Files_Fixed/GBPUSD_H1_FIXED.csv:
   metrics: {'timeframe': 'M60', 'accuracy': 0.27586206896551724, 'auc': 0.5641952983725136, 'f1': 0.2574833188072411, 'precision': 0.5045934885861258, 'recall': 0.5018115589777492, 'confusion_matrix': array([[201,  16],
       [656,  55]], dtype=int64), 'auc_pr': 0.8040437456024068}
   cv_results: {'accuracy': 0.23360258481421647, 'auc': 0.526019798917402, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

📊 เปรียบเทียบผลลัพธ์:
| Metric      | CV Avg    | Test Set |
|-------------|-----------|----------|
| Accuracy    | 0.2336    | 0.2759 |
| AUC         | 0.5260    | 0.5642 |
| F1 Score    | 0.0000    | 0.2575 |

🔍 ตรวจสอบเงื่อนไข Performance Tracking:
   ENABLE_PERFORMANCE_TRACKING: True
   TRACKING_AVAILABLE: True
   training_success: True
   เงื่อนไขรวม: True
✅ เข้าเงื่อนไข Performance Tracking - จะบันทึกไฟล์
🔍 Debug metrics from result_dict: {'timeframe': 'M60', 'accuracy': 0.27586206896551724, 'auc': 0.5641952983725136, 'f1': 0.2574833188072411, 'precision': 0.5045934885861258, 'recall': 0.5018115589777492, 'confusion_matrix': array([[201,  16],
       [656,  55]], dtype=int64), 'auc_pr': 0.8040437456024068}
🔍 Debug cv_results from result_dict: {'accuracy': 0.23360258481421647, 'auc': 0.526019798917402, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Final model_metrics: {'avg_accuracy': 0.27586206896551724, 'avg_f1_score': 0.2574833188072411, 'avg_auc': 0.5641952983725136, 'total_train_samples': 2788, 'total_test_samples': 928}
⚠️ เกิดข้อผิดพลาดในการโหลด thresholds: argument of type 'float' is not iterable

🏗️ เปิดใช้งาน load time filters
กำลังโหลด time filters จาก: LightGBM/Multi/thresholds/M60_GBPUSD_time_filters.pkl
✅ โหลด time filters สำเร็จ (M60_GBPUSD)
🔍 Debug loaded time_filters: {'days': [3, 1, 4], 'hours': [19, 20, 16, 5, 8, 18, 12, 13], 'detailed_stats': {'days': {'Monday': {'win_rate': 0.0, 'expectancy': 79.02721088435109, 'total_trades': 147.0, 'day_index': 0, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Tuesday': {'win_rate': 0.0, 'expectancy': 98.76190476190149, 'total_trades': 189.0, 'day_index': 1, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Wednesday': {'win_rate': 0.0, 'expectancy': 57.55932203389653, 'total_trades': 177.0, 'day_index': 2, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Thursday': {'win_rate': 0.0, 'expectancy': 112.00497512437558, 'total_trades': 201.0, 'day_index': 3, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Friday': {'win_rate': 0.0, 'expectancy': 87.3691588785022, 'total_trades': 214.0, 'day_index': 4, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}}, 'hours': {3: {'win_rate': 0.0, 'expectancy': 68.96226415094102, 'total_trades': 53.0, 'strong_buy_rate': 0.0}, 4: {'win_rate': 0.0, 'expectancy': 55.347826086955145, 'total_trades': 46.0, 'strong_buy_rate': 0.0}, 5: {'win_rate': 0.0, 'expectancy': 122.02941176470073, 'total_trades': 34.0, 'strong_buy_rate': 0.0}, 6: {'win_rate': 0.0, 'expectancy': 72.14285714285316, 'total_trades': 28.0, 'strong_buy_rate': 0.0}, 7: {'win_rate': 0.0, 'expectancy': 25.17647058823304, 'total_trades': 17.0, 'strong_buy_rate': 0.0}, 8: {'win_rate': 0.0, 'expectancy': 107.89999999999559, 'total_trades': 30.0, 'strong_buy_rate': 0.0}, 9: {'win_rate': 0.0, 'expectancy': 69.40350877192795, 'total_trades': 57.0, 'strong_buy_rate': 0.0}, 10: {'win_rate': 0.0, 'expectancy': 78.97183098591213, 'total_trades': 71.0, 'strong_buy_rate': 0.0}, 11: {'win_rate': 0.0, 'expectancy': 67.92647058823326, 'total_trades': 68.0, 'strong_buy_rate': 0.0}, 12: {'win_rate': 0.0, 'expectancy': 96.18867924528055, 'total_trades': 53.0, 'strong_buy_rate': 0.0}, 13: {'win_rate': 0.0, 'expectancy': 84.3728813559287, 'total_trades': 59.0, 'strong_buy_rate': 0.0}, 14: {'win_rate': 0.0, 'expectancy': 82.86206896551454, 'total_trades': 58.0, 'strong_buy_rate': 0.0}, 15: {'win_rate': 0.0, 'expectancy': 64.1944444444431, 'total_trades': 72.0, 'strong_buy_rate': 0.0}, 16: {'win_rate': 0.0, 'expectancy': 129.33333333333061, 'total_trades': 75.0, 'strong_buy_rate': 0.0}, 17: {'win_rate': 0.0, 'expectancy': 77.45783132529954, 'total_trades': 83.0, 'strong_buy_rate': 0.0}, 18: {'win_rate': 0.0, 'expectancy': 105.8260869565204, 'total_trades': 46.0, 'strong_buy_rate': 0.0}, 19: {'win_rate': 0.0, 'expectancy': 144.27777777777433, 'total_trades': 36.0, 'strong_buy_rate': 0.0}, 20: {'win_rate': 0.0, 'expectancy': 137.35714285714025, 'total_trades': 42.0, 'strong_buy_rate': 0.0}}}, 'adaptive_settings': {'min_win_rate': 0.3, 'min_expectancy': 0.0, 'adaptive_threshold': True, 'confidence_level': 0.8}, 'time_blocks': ['12:00-13:59', '18:00-20:59'], 'performance_comparison': {'filtered_vs_all': {'win_rate_improvement': 0.0, 'expectancy_improvement': 41.420705424725796, 'filtered_trades': 246, 'total_trades': 928, 'coverage': 0.2650862068965517, 'filtered_win_rate': 0.0, 'all_win_rate': 0.0}, 'best_day': {'win_rate': 0.0, 'total': 147.0, 'expectancy': 79.02721088435109, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0, 'combined_score': 31.61088435374044}, 'best_hour': {'win_rate': 0.0, 'total': 53.0, 'expectancy': 68.96226415094102, 'strong_buy_rate': 0.0, 'combined_score': 27.58490566037641}}}

🎯 กำลังเรียกใช้ record_model_performance...

🏗️ เปิดใช้งาน record model performance
   Symbol: GBPUSD, Timeframe: M60
   ENABLE_PERFORMANCE_TRACKING: True
   TRACKING_AVAILABLE: True
🔍 Debug trade_df:
   Shape: (16144, 338)
   Columns: ['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']
   ใช้ Trade Type แทน Signal
   Trade Type counts: {'Buy': 8393, 'Sell': 7751}
🔍 calculate_trade_metrics: input shape = (8393, 338)
   📊 Total trades: 8393
   💰 Winning trades: 1085/8393
   💰 Total profit: -185366.00
   ✅ Calculated metrics: {'count': 8393, 'win_rate': 12.92743953294412, 'expectancy': -22.085785773857957, 'trade_accuracy': 0.1292743953294412, 'trade_f1_score': 0.15512927439532945, 'trade_auc': 0.5517097581317765}
🔍 calculate_trade_metrics: input shape = (7751, 338)
   📊 Total trades: 7751
   💰 Winning trades: 1095/7751
   💰 Total profit: -147380.00
   ✅ Calculated metrics: {'count': 7751, 'win_rate': 14.127209392336473, 'expectancy': -19.014320732814404, 'trade_accuracy': 0.14127209392336473, 'trade_f1_score': 0.16952651270803767, 'trade_auc': 0.5565088375693459}

🏗️ เปิดใช้งาน format time filters display
🔍 Debug _compare_with_previous:
   Key: GBPUSD_M60
   Current F1: 0.2574833188072411
   Current AUC: 0.5641952983725136
   Previous F1: 0.5064981107431077
   Previous AUC: 0.5441093546442668
   Summary length: 2
⚠️ ⚠️ โมเดลไม่ดีขึ้น! F1 เปลี่ยน -0.2490, AUC เปลี่ยน 0.0201
🚨 โมเดลไม่ดีขึ้น - พิจารณาไม่บันทึกโมเดลนี้
🚨 โมเดลไม่ดีขึ้น - พิจารณาไม่บันทึกโมเดลนี้
🔍 Debug: กำลังเตรียมข้อมูลสำหรับบันทึกผลลัพธ์การเปรียบเทียบ (Multi-Model)
   - Symbol: GBPUSD, Timeframe: M60
   - Buy_sell stats (calculated): {'win_rate': 0.1350346878097126, 'expectancy': -20.61112487611709, 'profit_factor': 0.7137232937832023, 'count': 16144, 'max_drawdown': inf}
   - Performance data prepared: {'symbol': 'GBPUSD', 'timeframe': 'M60', 'entry_config': 'config_1_enhanced_signal', 'entry_config_name': 'Enhanced MACD Signal', 'entry_config_description': 'ใช้ macd_signal พร้อมเงื่อนไข volume และ pullback เพิ่มเติม', 'win_rate': 0.1350346878097126, 'expectancy': -20.61112487611709, 'profit_factor': 0.7137232937832023, 'num_trades': 16144, 'max_drawdown': inf, 'model_accuracy': 0.27586206896551724, 'model_auc': 0.5641952983725136, 'model_f1': 0.2574833188072411, 'training_date': '2025-10-01T16:43:31.235503', 'training_type': 'unknown', 'num_features': 33}

🏗️ เปิดใช้งาน save entry config performance

🏗️ เปิดใช้งาน get entry config results folder

🏗️ เปิดใช้งาน get entry config folder name
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models\M60_GBPUSD
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results\M60_GBPUSD
path บันทึกไฟล์ performance_summary.json LightGBM/Entry_config_1_enhanced_signal\results\M60_GBPUSD
✅ บันทึกผลการประเมิน config_1_enhanced_signal สำหรับ GBPUSD MM60 ที่: LightGBM/Entry_config_1_enhanced_signal\results\M60_GBPUSD\performance_summary.json
   📁 ขนาดไฟล์: 774 bytes
✅ บันทึกผลลัพธ์การเปรียบเทียบ Entry Config เสร็จสิ้น (Multi-Model)

✅ เสร็จสิ้นการประมวลผลกลุ่ม M60
📊 ประมวลผล: 1 ไฟล์
📈 ผลลัพธ์: 1 รายการ

📊 ไม่มีการปรับ threshold ในรอบนี้
🔍 Debug: round_results = <class 'dict'>
✅ กลุ่ม M60 สำเร็จ
📊 ผลลัพธ์กลุ่ม M60: สำเร็จ 1, ผิดพลาด 0
⏱️ เวลาที่ใช้: 221.2 วินาที (3.7 นาที)
📈 เฉลี่ยต่อไฟล์: 221.2 วินาที/ไฟล์

────────────────────────────────────────────────────────────
📋 สรุปรอบที่ 1:
   ⏱️ เวลาที่ใช้: 221.2 วินาที (3.7 นาที)
   ✅ ไฟล์สำเร็จ: 1
   ❌ ไฟล์ผิดพลาด: 0
   ⏰ เวลาสิ้นสุด: 16:43:31
   📊 M60: 221.2s (221.2s/ไฟล์)

============================================================
⏭️ ข้ามการวิเคราะห์ Feature Importance ข้าม Assets
============================================================
💡 หมายเหตุ: การวิเคราะห์จะทำงานเฉพาะเมื่อเทรนโมเดลใหม่และมีผลลัพธ์
============================================================

================================================================================
🎉 สรุปการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-10-01 16:39:50
⏰ เวลาสิ้นสุด: 2025-10-01 16:43:31
⏱️ เวลาที่ใช้ทั้งหมด: 221.2 วินาที (3.7 นาที)

🚀 ประสิทธิภาพการทำงาน:
   📈 ประมวลผลได้: 16 ไฟล์/ชั่วโมง
================================================================================
💾 บันทึกผลการทดสอบลงใน 'LightGBM/Multi_Time\time_test.txt' เรียบร้อยแล้ว

================================================================================
💰 เริ่มการวิเคราะห์ทางการเงินรวม
================================================================================

================================================================================
🚀 เริ่มการวิเคราะห์ทางการเงินทั้งหมด
================================================================================
2025-10-01 16:43:31 | INFO     | FinancialAnalysis | 🚀 เริ่มการวิเคราะห์ทางการเงินแบบสมบูรณ์ | account_balance=1000
📊 พบข้อมูลการเทรด: 16144 รายการ
📊 บันทึกกราฟที่: Financial_Analysis_Results/trading_performance_analysis.png
💾 บันทึกการวิเคราะห์สมบูรณ์:
   📄 Summary: Financial_Analysis_Results/complete_financial_analysis.json
   📊 Risk Table: Financial_Analysis_Results/risk_management_table.csv
   📝 Report: Financial_Analysis_Results/financial_analysis_report.txt

🎨 สร้างกราฟ Financial Analysis แบบแยกรายละเอียด
📊 พบ timeframes: ['M60']
📊 พบ symbols: ['GBPUSD']
✅ บันทึกกราฟ M60 ที่: Financial_Analysis_Results\trading_performance_M60.png
✅ บันทึกไฟล์ CSV M60 ที่: Financial_Analysis_Results\trading_summary_M60.csv
🎉 สร้างกราฟ Financial Analysis แบบแยกรายละเอียดเสร็จสิ้น
2025-10-01 16:43:57 | INFO     | FinancialAnalysis | ✅ การวิเคราะห์ทางการเงินเสร็จสมบูรณ์ | account_balance=1000 | total_trades=16144 | total_profit_usd=-332746.********** | max_drawdown_usd=334395.*********** | recommended_lot_size=5.980950672108827e-05 | max_risk_percentage=2.0

============================================================
📈 FINANCIAL ANALYSIS SUMMARY
============================================================
💰 Account Balance: $1,000.00
📊 Total Trades: 16144
💵 Total Profit (1.0 lot): $-332,746.00
📉 Max Drawdown (1.0 lot): $334,395.00
🎯 Recommended Lot Size: 0.0001
⚠️ Max Risk: 2.00%
============================================================

🎉 การวิเคราะห์ทางการเงินเสร็จสมบูรณ์!
📁 ผลลัพธ์บันทึกที่: Financial_Analysis_Results

📈 สรุปผลการวิเคราะห์:
   💰 ยอดเงินในบัญชี: $1,000.00
   📊 จำนวนการเทรดทั้งหมด: 16144
   💵 กำไรรวม (1.0 lot): $-332,746.00
   📉 Drawdown สูงสุด (1.0 lot): $334,395.00
   🎯 ขนาดล็อตที่แนะนำ: 0.0001
   ⚠️ ความเสี่ยงสูงสุด: 2.00%

🎨 สร้างกราฟแยกรายละเอียดเพิ่มเติม...
⚠️ ไม่สามารถสร้างกราฟแยกรายละเอียดเพิ่มเติม: name 'create_enhanced_financial_charts' is not defined
🎉 การวิเคราะห์ทางการเงินเสร็จสมบูรณ์!
